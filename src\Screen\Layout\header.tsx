import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {StyleSheet, View, Image, Text, TouchableOpacity} from 'react-native';
import {Divider} from 'react-native-paper';
import {Winicon} from 'wini-mobile-components';

interface ScreenHeaderProps {
  title: string;
  showAction?: boolean;
  showDivider?: boolean;
  onBack?: () => void;
}

const ScreenHeader = ({
  title,
  showAction = false,
  showDivider = true,
  onBack,
}: ScreenHeaderProps) => {
  const navigation = useNavigation<any>();

  const handleBack = () => {
    if (onBack) return onBack();
    navigation.goBack();
  };

  return (
    <View>
      <View style={styles.header}>
        <Image source={require('../../assets/images/header_group.png')} />
      </View>
      <View style={styles.navigator}>
        <TouchableOpacity style={styles.back} onPress={handleBack}>
          <View style={styles.backContent}>
            <Winicon
              src="outline/arrows/left-arrow"
              size={14}
              color={'black'}
              style={styles.navigatorIcon}
            />
          </View>
          <View style={styles.backContent}>
            <Image
              source={require('../../assets/images/logo.png')}
              style={styles.navigatorImage}
            />
          </View>
        </TouchableOpacity>
        <View style={styles.titleNavigatorContainer}>
          <Text style={styles.titleNavigator}>{title}</Text>
        </View>
        {showAction ? (
          <View style={styles.actions}>
            <TouchableOpacity>
              <Image
                source={require('../../assets/images/icon_navigator.png')}
                style={styles.navigatorImage}
              />
            </TouchableOpacity>
            <TouchableOpacity>
              <Image
                source={require('../../assets/images/icon_bell_navigator.png')}
                style={styles.navigatorImage}
              />
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.actions} />
        )}
      </View>
      {showDivider && <Divider />}
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  navigator: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: 18,
    width: '100%',
  },
  navigatorImage: {
    width: 32,
    height: 32,
    borderRadius: 50,
  },
  navigatorIcon: {
    marginTop: 4,
  },
  titleNavigatorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleNavigator: {
    fontSize: 16,
    fontFamily: 'roboto',
    marginLeft: 20,
  },
  back: {
    display: 'flex',
    flexDirection: 'row',
    marginLeft: 15,
  },
  backContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actions: {
    minWidth: 60,
    flexDirection: 'row',
    marginRight: 15,
    gap: 2,
  },
});

export default ScreenHeader;
