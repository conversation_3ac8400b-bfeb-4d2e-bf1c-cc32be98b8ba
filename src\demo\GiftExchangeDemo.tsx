import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Winicon } from 'wini-mobile-components';
import LinearGradient from 'react-native-linear-gradient';
import { ColorThemes } from '../assets/skin/colors';
import { TypoSkin } from '../assets/skin/typography';
import GiftCard from '../components/Gift/GiftCard';
import ExchangeHistoryCard from '../components/Gift/ExchangeHistoryCard';

// Demo data
const demoGifts = [
  {
    Id: '1',
    Name: 'Combo mỹ phẩm nhập mỹ xách tay gồm: phấn kem nền, kẻ mắt...',
    Description: 'Set mỹ phẩm cao cấp nhập khẩu từ Mỹ',
    Img: 'demo-gift-1.jpg',
    Points: 500,
    Status: 1,
    Quantity: 2,
    Category: 'beauty',
  },
  {
    Id: '2',
    Name: 'Voucher mua sắm 100K',
    Description: 'Voucher giảm giá 100.000đ cho đơn hàng từ 500K',
    Img: 'demo-gift-2.jpg',
    Points: 200,
    Status: 1,
    Quantity: 10,
    Category: 'voucher',
  },
];

const demoExchangeHistory = [
  {
    Id: '1',
    GiftId: '1',
    CustomerId: 'demo-customer',
    Points: 500,
    Status: 0, // Chờ duyệt
    DateCreated: new Date().toISOString(),
    Gift: demoGifts[0],
  },
  {
    Id: '2',
    GiftId: '2',
    CustomerId: 'demo-customer',
    Points: 200,
    Status: 1, // Đã duyệt
    DateCreated: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    Gift: demoGifts[1],
  },
];

const GiftExchangeDemo = () => {
  const [activeTab, setActiveTab] = React.useState<'gifts' | 'pending' | 'history'>('gifts');
  const currentPoints = 1000;
  const pendingCount = 10;

  const handleGiftPress = (gift: any) => {
    console.log('Gift pressed:', gift.Name);
  };

  const handleExchangeGift = (gift: any) => {
    console.log('Exchange gift:', gift.Name);
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Gift Exchange Demo</Text>

      {/* Points Display */}
      <LinearGradient
        colors={['#4FC3F7', '#2196F3']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.pointsContainer}>
        <View style={styles.pointsContent}>
          <View style={styles.pointsIconContainer}>
            <Winicon
              src="fill/shopping/diamond"
              size={24}
              color="#FFA500"
            />
          </View>
          <Text style={styles.pointsLabel}>Bạn đang có</Text>
          <Text style={styles.pointsValue}>
            {currentPoints.toLocaleString()}$
          </Text>
        </View>
      </LinearGradient>

      {/* Tabs */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'gifts' && styles.activeTab]}
          onPress={() => setActiveTab('gifts')}>
          <Winicon
            src="outline/shopping/gift"
            size={16}
            color={activeTab === 'gifts' ? 'white' : ColorThemes.light.neutral_text_subtitle_color}
          />
          <Text style={[styles.tabText, activeTab === 'gifts' && styles.activeTabText]}>
            Đổi quà
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'pending' && styles.activeTab]}
          onPress={() => setActiveTab('pending')}>
          <Winicon
            src="outline/time/clock"
            size={16}
            color={activeTab === 'pending' ? 'white' : ColorThemes.light.neutral_text_subtitle_color}
          />
          <View style={styles.tabTextContainer}>
            <Text style={[styles.tabText, activeTab === 'pending' && styles.activeTabText]}>
              Chờ duyệt
            </Text>
            {pendingCount > 0 && (
              <View style={styles.tabBadge}>
                <Text style={styles.tabBadgeText}>
                  {pendingCount > 99 ? '99+' : pendingCount}
                </Text>
              </View>
            )}
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'history' && styles.activeTab]}
          onPress={() => setActiveTab('history')}>
          <Winicon
            src="outline/time/history"
            size={16}
            color={activeTab === 'history' ? 'white' : ColorThemes.light.neutral_text_subtitle_color}
          />
          <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>
            Lịch sử
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {activeTab === 'gifts' && (
          <View>
            <Text style={styles.sectionTitle}>Danh sách quà tặng</Text>
            {demoGifts.map((gift, index) => (
              <GiftCard
                key={index}
                item={gift}
                onPress={handleGiftPress}
                onExchange={handleExchangeGift}
                currentPoints={currentPoints}
              />
            ))}
          </View>
        )}

        {activeTab === 'pending' && (
          <View>
            <Text style={styles.sectionTitle}>Giao dịch chờ duyệt</Text>
            {demoExchangeHistory
              .filter(item => item.Status === 0)
              .map((item, index) => (
                <ExchangeHistoryCard key={index} item={item} />
              ))}
          </View>
        )}

        {activeTab === 'history' && (
          <View>
            <Text style={styles.sectionTitle}>Lịch sử đổi quà</Text>
            {demoExchangeHistory.map((item, index) => (
              <ExchangeHistoryCard key={index} item={item} />
            ))}
          </View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  title: {
    ...TypoSkin.heading1,
    textAlign: 'center',
    marginVertical: 20,
    color: ColorThemes.light.neutral_text_title_color,
  },
  pointsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  pointsContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  pointsIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pointsLabel: {
    ...TypoSkin.body1,
    color: 'white',
    flex: 1,
  },
  pointsValue: {
    ...TypoSkin.heading3,
    color: 'white',
    fontWeight: '700',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 6,
  },
  activeTab: {
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  tabText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontSize: 12,
  },
  activeTabText: {
    color: 'white',
    fontWeight: '600',
  },
  tabTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  tabBadge: {
    backgroundColor: '#FF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  tabBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingBottom: 20,
  },
  sectionTitle: {
    ...TypoSkin.title2,
    color: ColorThemes.light.neutral_text_title_color,
    marginHorizontal: 16,
    marginVertical: 12,
  },
});

export default GiftExchangeDemo;
