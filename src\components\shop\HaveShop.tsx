import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { TextField, Winicon } from 'wini-mobile-components';
import { RootScreen } from '../../router/router';
import { Title, TypeMenuShop } from '../../Config/Contanst';
import { ScrollView } from 'react-native-gesture-handler';
import LeaderShopInfo from '../Field/LeaderShopInfo';
import { OrderData } from '../../mock/shopData';
import { useDispatch } from 'react-redux';
import { OrderActions } from '../../redux/reducers/OrderReducer';
import { useSelectorShopState } from '../../redux/hook/shopHook ';
import { useSelectorOrderState } from '../../redux/hook/orderHook ';
import { useSelectorCustomerState } from '../../redux/hook/customerHook';
import { ColorThemes } from '../../assets/skin/colors';
import { TypoSkin } from '../../assets/skin/typography';
import MenuHaveShop from '../Field/menu/MenuHaveShop/MenuHaveShop';
import MenuCards from '../Field/menu/MenuHaveShop/MenuCards';
import iconSvg from '../../svg/icon';

interface HaveShopProps {
  shop: any[];
}
const HaveShop = (props: HaveShopProps) => {
  const navigation = useNavigation<any>();
  const [getNewOrder, SetGetNewOrder] = useState<number>(0);
  const [getProcessingOrder, SetGetProcessingOrder] = useState<number>(0);
  const [getCancelOrder, SetGetCancelOrder] = useState<number>(0);
  const dispatch = useDispatch<any>();
  const orderInfo = useSelectorOrderState().data;
  const shopInfo = useSelectorShopState().data;

  const handleNavigateOrders = (
    order: string,
    type: string,
    status?: number,
    numberOrder?: number,
  ) => {
    navigation.navigate(order, {
      type: type,
      status: status,
      numberOrder: numberOrder,
    });
  };
  useEffect(() => {
    dispatch(OrderActions.getInforOrder(shopInfo[0]?.Id));
  }, []);
  useEffect(() => {
    SetGetNewOrder(orderInfo?.NewOrder?.number);
    SetGetProcessingOrder(orderInfo?.ProcessOrder?.number);
    SetGetCancelOrder(orderInfo?.CancelOrder?.number);
  }, [orderInfo]);
  return (
    <ScrollView>
      <View style={styles.container}>
        <View style={{ paddingBottom: 100 }}>
          <View style={styles.navBar}>
            <MenuHaveShop
              svgIcon={iconSvg.walletAction}
              title="Đơn hàng mới"
              getNewOrder={getNewOrder}
              order={RootScreen.OrderDetail}
              type={Title.New}
              status={1}
              numberOrder={getNewOrder}
            />
            <MenuHaveShop
              svgIcon={iconSvg.deliveryIcon}
              title="Đang xử lý"
              getNewOrder={getProcessingOrder}
              order={RootScreen.OrderDetail}
              type={Title.Processing}
              status={1}
              numberOrder={getProcessingOrder}
            />
            <MenuHaveShop
              svgIcon={iconSvg.done}
              title="Hoàn thành"
              order={RootScreen.OrderDetail}
              type={Title.Done}
              status={1}
            />
            <MenuHaveShop
              svgIcon={iconSvg.cancel}
              title="Hủy/hoàn"
              getNewOrder={getCancelOrder}
              order={RootScreen.OrderDetail}
              type={Title.Cancel}
              status={1}
              numberOrder={getCancelOrder}
            />
            <MenuHaveShop
              svgIcon={iconSvg.star}
              title="Đánh giá"
              order={RootScreen.Review}
              type={Title.Shop}
            />
          </View>
          <View
            style={{
              height: 63,
              maxWidth: 409,
              marginTop: 23,
              marginLeft: 7,
              marginRight: 3,
              backgroundColor: ColorThemes.light.primary_background,
              borderRadius: 10,
            }}>

            <Text
              style={{
                fontSize: 16,
                color: ColorThemes.light.neutral_text_title_color,
                fontWeight: '700',
                marginBottom: 3,
              }}>
              Số dư bán hàng
            </Text>
            <Text
              style={{
                ...TypoSkin.title4,
                color: ColorThemes.light.neutral_text_title_color,
                marginBottom: 3,
              }}>
              500.000 VNĐ
            </Text>
          </View>
          <View>
            <TouchableOpacity
            onPress={
              ()=>{
                navigation.push(RootScreen.ConfigAffiliate)
              }
            }

              style={{
                backgroundColor: ColorThemes.light.primary_main_color,
                borderRadius: 5,
                width: 64,
                height: 24,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 10,
                marginRight: 10,
                marginBottom: 29,
              }}>
              <Text
                style={{
                  flexDirection: 'column',
                  margin: 10,
                }}>
                <Text
                  style={{
                    fontSize: 16,
                    color: ColorThemes.light.neutral_text_title_color,
                    fontWeight: '700',
                    marginBottom: 3,
                  }}>
                  Số dư bán hàng
                </Text>
                <Text
                  style={{
                    ...TypoSkin.title4,
                    color: ColorThemes.light.neutral_text_title_color,
                    marginBottom: 3,
                  }}>
                  500.000 VNĐ
                </Text>
              </View>
              <View>
                <TouchableOpacity
                  style={{
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 5,
                    width: 64,
                    height: 24,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: 10,
                    marginRight: 10,
                    marginBottom: 29,
                  }}>
                  <Text
                    style={{
                      color: ColorThemes.light.neutral_absolute_background_color,
                    }}>
                    Rút tiền
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
          <View style={{ height: '67%' }}>

            <LeaderShopInfo shop={props.shop} />
            <MenuCards
              svgIcon={iconSvg.manageProduct}
              color="orange"
              title="QL sản phẩm"
              order={RootScreen.ManageProduct}
            />
            <MenuCards
              svgIcon={iconSvg.manageReport}
              color="orange"
              title="Báo cáo"
              order={RootScreen.ChartReport}
            />
            <MenuCards
              svgIcon={iconSvg.promotion}
              color="orange"
              title="Khuyến mại"
              order={""}
           

            />

          </View>
        </View>

      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    marginTop: 18,
    marginLeft: 20,
    marginRight: 13,
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'ColorThemes.light.neutral_absolute_background_color',
  },
  navItem: {
    alignItems: 'center',
    borderColor: '#00FFFF',
    borderWidth: 0.3,
    padding: 10,
    borderRadius: 10,
    maxWidth: 67,
    maxHeight: 68,
  },
  navText: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginTop: 4,
    textAlign: 'center',
  },
  navBadge: {
    position: 'absolute',
    top: 5,
    right: 4,
    backgroundColor: '#FF0000',
    color: '#fff',
    fontSize: 10,
    paddingHorizontal: 4,
    borderRadius: 10,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  cardHeaderLeft: {
    display: 'flex',
    flexDirection: 'row',
    alignContent: 'center',
    gap: 3,
    flex: 1,
  },
  cardHeaderRight: {},

  cardContent: {
    marginTop: 10,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  infoValue: {
    fontSize: 14,
    color: '#000',
    fontWeight: '500',
  },
  infoValueEdit: {
    lineHeight: 14,
    display: 'flex',
    alignItems: 'center',
    color: '#000',
    fontWeight: '500',
  },
  actionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    height: 70,
  },
  actionText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_title_color,
    marginLeft: 10,
  },

  input: {
    flex: 1,
    fontSize: 16,
    color: 'black',
    borderWidth: 0,
    backgroundColor: 'white',
    lineHeight: 14,
  },
});

export default HaveShop;
