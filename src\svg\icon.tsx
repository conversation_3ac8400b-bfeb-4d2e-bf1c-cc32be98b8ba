export default class iconSvg {
  static search = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M15.5 14C18.5376 14 21 11.5376 21 8.5C21 5.46243 18.5376 3 15.5 3C12.4624 3 10 5.46243 10 8.5
  C10 11.5376 12.4624 14 15.5 14Z" fill="currentColor"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M11 2C5.47715 2 1 6.47715 1 12C1 17.5228 5.47715 22 11 22C16.5228 22 21 17.5228 21 12C21 6.47715 16.5228 2 11 2ZM11 20C7.13401 20 4 16.866 4 12C4 7.13401 7.13401 4 11 4C14.866 4 1
  8.5 7.13401 18.5 12C18.5 16.866 14.866 20 11 20Z" fill="currentColor"/>
</svg>`;
  static giftExchange = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M20 12V22H4V12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M22 7H2V12H22V7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M12 22V7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M12 7H7.5C6.83696 7 6.20107 6.73661 5.73223 6.26777C5.26339 5.79893 5 5.16304 5 4.5C5 3.83696 5.26339 3.20107 5.73223 2.73223C6.20107 2.26339 6.83696 2 7.5 2C9 2 10 3 12 7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M12 7H16.5C17.163 7 17.7989 6.73661 18.2678 6.26777C18.7366 5.79893 19 5.16304 19 4.5C19 3.83696 18.7366 3.20107 18.2678 2.73223C17.7989 2.26339 17.163 2 16.5 2C15 2 14 3 12 7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;
  static filter = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.8333 4.5H22V6.16667H12.8333V4.5Z" fill="url(#paint0_linear_24155_11656)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M2 4.5H6.16667V6.16667H2V4.5Z" fill="url(#paint1_linear_24155_11656)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.5 11.1667H22V12.8333H19.5V11.1667Z" fill="url(#paint2_linear_24155_11656)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M2 11.1667H12.8333V12.8333H2V11.1667Z" fill="url(#paint3_linear_24155_11656)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.8333 17.8333H22V19.5H12.8333V17.8333Z" fill="url(#paint4_linear_24155_11656)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M2 17.8333H6.16667V19.5H2V17.8333Z" fill="url(#paint5_linear_24155_11656)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.83333 3.66667C6.91286 3.66667 6.16667 4.41286 6.16667 5.33333C6.16667 6.25381 6.91286 7 7.83333 7C8.75381 7 9.5 6.25381 9.5 5.33333C9.5 4.41286 8.75381 3.66667 7.83333 3.66667ZM4.5 5.33333C4.5 3.49238 5.99238 2 7.83333 2C9.67428 2 11.1667 3.49238 11.1667 5.33333C11.1667 7.17428 9.67428 8.66667 7.83333 8.66667C5.99238 8.66667 4.5 7.17428 4.5 5.33333Z" fill="url(#paint6_linear_24155_11656)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.5 10.3333C13.5795 10.3333 12.8333 11.0795 12.8333 12C12.8333 12.9205 13.5795 13.6667 14.5 13.6667C15.4205 13.6667 16.1667 12.9205 16.1667 12C16.1667 11.0795 15.4205 10.3333 14.5 10.3333ZM11.1667 12C11.1667 10.1591 12.6591 8.66667 14.5 8.66667C16.3409 8.66667 17.8333 10.1591 17.8333 12C17.8333 13.8409 16.3409 15.3333 14.5 15.3333C12.6591 15.3333 11.1667 13.8409 11.1667 12Z" fill="url(#paint7_linear_24155_11656)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.83333 17C6.91286 17 6.16667 17.7462 6.16667 18.6667C6.16667 19.5871 6.91286 20.3333 7.83333 20.3333C8.75381 20.3333 9.5 19.5871 9.5 18.6667C9.5 17.7462 8.75381 17 7.83333 17ZM4.5 18.6667C4.5 16.8257 5.99238 15.3333 7.83333 15.3333C9.67428 15.3333 11.1667 16.8257 11.1667 18.6667C11.1667 20.5076 9.67428 22 7.83333 22C5.99238 22 4.5 20.5076 4.5 18.6667Z" fill="url(#paint8_linear_24155_11656)"/>
<defs>
<linearGradient id="paint0_linear_24155_11656" x1="22" y1="2.6383" x2="1.29078" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
<linearGradient id="paint1_linear_24155_11656" x1="22" y1="2.6383" x2="1.29078" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
<linearGradient id="paint2_linear_24155_11656" x1="22" y1="2.6383" x2="1.29078" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
<linearGradient id="paint3_linear_24155_11656" x1="22" y1="2.6383" x2="1.29078" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
<linearGradient id="paint4_linear_24155_11656" x1="22" y1="2.6383" x2="1.29078" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
<linearGradient id="paint5_linear_24155_11656" x1="22" y1="2.6383" x2="1.29078" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
<linearGradient id="paint6_linear_24155_11656" x1="22" y1="2.6383" x2="1.29078" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
<linearGradient id="paint7_linear_24155_11656" x1="22" y1="2.6383" x2="1.29078" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
<linearGradient id="paint8_linear_24155_11656" x1="22" y1="2.6383" x2="1.29078" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
</defs>
</svg>
`;
  static notification = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.99968 1.78212C9.39349 1.49414 8.71538 1.33301 7.99963 1.33301C5.4223 1.33301 3.33297 3.42235 3.33297 5.99967V6.46908C3.33297 7.0324 3.16622 7.58313 2.85374 8.05184L2.08801 9.20045C1.38858 10.2496 1.92254 11.6756 3.13901 12.0074C6.32131 12.8753 9.67795 12.8753 12.8603 12.0074C14.0767 11.6756 14.6107 10.2496 13.9113 9.20045L13.1455 8.05184C12.833 7.58313 12.6663 7.0324 12.6663 6.46908V5.99967C12.6663 5.28393 12.5052 4.60581 12.2172 3.99963" stroke="url(#paint0_linear_24155_11629)" stroke-width="1.5" stroke-linecap="round"/>
<path d="M11 12.667C10.5633 13.8322 9.38503 14.667 8 14.667C7.83702 14.667 7.6769 14.6554 7.52051 14.6331M5 12.667C5.17804 13.142 5.47935 13.5622 5.86768 13.8932" stroke="url(#paint1_linear_24155_11629)" stroke-width="1.5" stroke-linecap="round"/>
<defs>
<linearGradient id="paint0_linear_24155_11629" x1="1.77661" y1="1.69445" x2="13.5258" y2="13.7661" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
<linearGradient id="paint1_linear_24155_11629" x1="5" y1="14.6032" x2="6.31313" y2="10.9201" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
</defs>
</svg>
`;
  static card = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M1.35688 0.164151C0.982201 -0.107743 0.450662 -0.034272 0.169654 0.328253C-0.111355 0.690779 -0.0354208 1.20508 0.339257 1.47697L1.78746 2.5279L4.11017 12.2665C4.28776 13.0111 4.973 13.5385 5.76277 13.5385H13.1443C13.6127 13.5385 13.9924 13.1712 13.9924 12.718C13.9924 12.2648 13.6127 11.8975 13.1443 11.8975L5.76277 11.8975L5.54269 10.9747L11.7439 10.4747C13.7421 10.3136 15.3505 8.82027 15.5992 6.8952L15.9867 3.89585C16.1132 2.9164 15.3239 2.05128 14.3037 2.05128H3.41039C3.30595 1.7183 3.09439 1.42502 2.80509 1.21508L1.35688 0.164151ZM3.80579 3.69231L5.15735 9.35909L11.6031 8.83937C12.802 8.74271 13.767 7.84669 13.9162 6.69165L14.3037 3.69231H3.80579Z" fill="url(#paint0_linear_24155_11634)"/>
<path d="M6.78415 15.1795C6.78415 15.6326 6.40448 16 5.93613 16C5.46778 16 5.08811 15.6326 5.08811 15.1795C5.08811 14.7263 5.46778 14.359 5.93613 14.359C6.40448 14.359 6.78415 14.7263 6.78415 15.1795Z" fill="url(#paint1_linear_24155_11634)"/>
<path d="M11.8723 16C12.3406 16 12.7203 15.6326 12.7203 15.1795C12.7203 14.7263 12.3406 14.359 11.8723 14.359C11.4039 14.359 11.0242 14.7263 11.0242 15.1795C11.0242 15.6326 11.4039 16 11.8723 16Z" fill="url(#paint2_linear_24155_11634)"/>
<defs>
<linearGradient id="paint0_linear_24155_11634" x1="16" y1="0.510638" x2="-0.567376" y2="16" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
<linearGradient id="paint1_linear_24155_11634" x1="16" y1="0.510638" x2="-0.567376" y2="16" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
<linearGradient id="paint2_linear_24155_11634" x1="16" y1="0.510638" x2="-0.567376" y2="16" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
</defs>
</svg>
`;
  static menu = `<svg width="26" height="22" viewBox="0 0 26 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.75 8.8125H24.75C24.8875 8.8125 25 8.7 25 8.5625V6.8125C25 6.675 24.8875 6.5625 24.75 6.5625H9.75C9.6125 6.5625 9.5 6.675 9.5 6.8125V8.5625C9.5 8.7 9.6125 8.8125 9.75 8.8125ZM9.5 15.1875C9.5 15.325 9.6125 15.4375 9.75 15.4375H24.75C24.8875 15.4375 25 15.325 25 15.1875V13.4375C25 13.3 24.8875 13.1875 24.75 13.1875H9.75C9.6125 13.1875 9.5 13.3 9.5 13.4375V15.1875ZM25.25 0H0.75C0.6125 0 0.5 0.1125 0.5 0.25V2C0.5 2.1375 0.6125 2.25 0.75 2.25H25.25C25.3875 2.25 25.5 2.1375 25.5 2V0.25C25.5 0.1125 25.3875 0 25.25 0ZM25.25 19.75H0.75C0.6125 19.75 0.5 19.8625 0.5 20V21.75C0.5 21.8875 0.6125 22 0.75 22H25.25C25.3875 22 25.5 21.8875 25.5 21.75V20C25.5 19.8625 25.3875 19.75 25.25 19.75ZM1.45 15.0656L6.33438 11.2188C6.36726 11.1929 6.39385 11.1599 6.41213 11.1223C6.43041 11.0847 6.43991 11.0434 6.43991 11.0016C6.43991 10.9597 6.43041 10.9184 6.41213 10.8808C6.39385 10.8432 6.36726 10.8102 6.33438 10.7844L1.45 6.93437C1.26875 6.79062 1 6.91875 1 7.15V14.8469C0.999983 14.8991 1.01465 14.9502 1.04233 14.9945C1.07002 15.0387 1.10959 15.0743 1.15653 15.0971C1.20347 15.1199 1.25589 15.1291 1.30779 15.1235C1.35968 15.1179 1.40896 15.0979 1.45 15.0656Z" fill="url(#paint0_linear_24155_11655)"/>
<defs>
<linearGradient id="paint0_linear_24155_11655" x1="25.5" y1="0.702127" x2="2.71007" y2="24.9147" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BC1E4"/>
<stop offset="0.306839" stop-color="#FFC043"/>
<stop offset="0.716385" stop-color="#FFC043"/>
<stop offset="1" stop-color="#DA251D"/>
</linearGradient>
</defs>
</svg>
`;
  static logo = `<svg width="200" height="49" viewBox="0 0 200 49" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.7745 40.1052C12.3393 40.1176 9.94409 39.4849 7.83166 38.2711C5.71923 37.0573 3.96468 35.3057 2.74583 33.1937C1.52699 31.0817 0.887178 28.6845 0.891255 26.245C0.895332 23.8055 1.54315 21.4104 2.76905 19.3026C3.99494 17.1947 5.75535 15.4489 7.87182 14.2423C9.9883 13.0356 12.3856 12.4108 14.8208 12.4314C17.256 12.452 19.6424 13.1171 21.7383 14.3593C23.8342 15.6015 25.565 17.3767 26.7552 19.505C24.4245 20.7288 24.2369 20.8415 21.6436 22.1029C20.9415 20.9031 19.9379 19.9086 18.7327 19.2183C17.5275 18.528 16.1628 18.1661 14.7745 18.1685C13.6949 18.1407 12.6206 18.3297 11.615 18.7243C10.6094 19.119 9.69287 19.7113 8.91942 20.4664C8.14598 21.2215 7.53128 22.1241 7.11156 23.121C6.69185 24.1178 6.47562 25.1888 6.47562 26.2707C6.47562 27.3526 6.69185 28.4235 7.11156 29.4204C7.53128 30.4172 8.14598 31.3198 8.91942 32.0749C9.69287 32.83 10.6094 33.4224 11.615 33.817C12.6206 34.2117 13.6949 34.4007 14.7745 34.3728C16.2304 34.3842 17.6613 33.9941 18.9107 33.2453C20.16 32.4964 21.1797 31.4177 21.8579 30.1272C24.4566 31.469 24.537 31.6193 26.9749 32.7304C25.8117 34.962 24.0584 36.8302 21.9067 38.1309C19.7549 39.4316 17.2876 40.1146 14.7745 40.1052Z" fill="#1C33FF"/>
<path d="M49.0023 28.3561H36.234V40.1054H30.3401V12.4309H36.234V22.4412H49.0023V12.5114H54.9445V40.1054H49.0023V28.3561Z" fill="#1C33FF"/>
<path d="M114.847 21.0935V40.105H109.58V12.511H116.498L128.752 29.6868V12.511H133.842V40.105H128.752L114.847 21.0935Z" fill="#1C33FF"/>
<path d="M138.589 12.5864H144.617V40.0677H138.589V12.5864Z" fill="#1C33FF"/>
<path d="M163.735 40.105H156.882L147.934 12.511H154.203L160.338 31.9143H160.418L166.628 12.511H172.881L163.735 40.105Z" fill="#1C33FF"/>
<path d="M186.1 40.1055C183.375 40.1023 180.712 39.2897 178.448 37.7705C176.185 36.2513 174.421 34.0937 173.382 31.5706C172.342 29.0475 172.073 26.2722 172.608 23.5958C173.143 20.9194 174.459 18.4621 176.388 16.5347C178.317 14.6072 180.774 13.2962 183.447 12.7675C186.12 12.2388 188.89 12.516 191.406 13.5643C193.922 14.6125 196.071 16.3846 197.581 18.6564C199.092 20.9282 199.896 23.5977 199.891 26.3273C199.893 28.1402 199.537 29.9356 198.844 31.6103C198.151 33.2851 197.134 34.8062 195.853 36.0864C194.571 37.3666 193.05 38.3807 191.376 39.0704C189.703 39.7601 187.909 40.1118 186.1 40.1055ZM177.971 26.3273C177.986 27.9254 178.472 29.4834 179.369 30.8055C180.265 32.1276 181.531 33.1547 183.009 33.7578C184.486 34.3608 186.109 34.5128 187.672 34.1947C189.236 33.8765 190.67 33.1025 191.796 31.9698C192.921 30.8371 193.687 29.3964 193.998 27.8288C194.308 26.2611 194.149 24.6366 193.54 23.1593C192.931 21.6821 191.9 20.4181 190.576 19.5263C189.252 18.6345 187.695 18.1548 186.1 18.1473C183.943 18.1656 181.88 19.0345 180.359 20.5657C178.838 22.0968 177.98 24.1669 177.971 26.3273Z" fill="#1C33FF"/>
<path d="M68.1628 0.89624H101.694C102.902 0.89624 104.06 1.37691 104.914 2.23251C105.768 3.08812 106.248 4.24856 106.248 5.45856V39.0373C106.248 40.3363 105.993 41.6227 105.496 42.8228C105 44.023 104.273 45.1135 103.356 46.0321C102.439 46.9507 101.35 47.6793 100.152 48.1764C98.9541 48.6736 97.67 48.9294 96.3732 48.9294H62.8476C61.6397 48.9294 60.4813 48.4488 59.6272 47.5932C58.773 46.7376 58.2932 45.5771 58.2932 44.3671V10.7884C58.2932 8.16579 59.3329 5.65049 61.1836 3.7955C63.0344 1.94051 65.5447 0.897663 68.1628 0.89624Z" fill="url(#paint0_linear_24155_11762)"/>
<path d="M62.387 40.0298L74.866 12.511H80.9528L93.2442 40.0298H87.3878L84.7088 33.9324H70.7777L67.8951 40.0298H62.387ZM77.9897 17.6852L73.3657 28.3396H82.4745L78.0058 17.6852H77.9897Z" fill="white"/>
<path d="M96.0947 12.49H102.144V40.0303H96.0947V12.49Z" fill="white"/>
<defs>
<linearGradient id="paint0_linear_24155_11762" x1="92.8958" y1="16.0378" x2="39.6944" y2="60.3085" gradientUnits="userSpaceOnUse">
<stop offset="0.07" stop-color="#008DFF"/>
<stop offset="0.12" stop-color="#0382FF"/>
<stop offset="0.31" stop-color="#0E60FF"/>
<stop offset="0.5" stop-color="#1647FF"/>
<stop offset="0.66" stop-color="#1A38FF"/>
<stop offset="0.8" stop-color="#1C33FF"/>
</linearGradient>
</defs>
</svg>
`;
  static delivery = `<svg width="23" height="16" viewBox="0 0 23 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 0V2H14V6V8V11H8.2207C7.67163 10.3907 6.88492 10 6 10C5.11508 10 4.32837 10.3907 3.7793 11H3V10L5 8H1V13H3C3 14.657 4.343 16 6 16C7.657 16 9 14.657 9 13H15C15 14.657 16.343 16 18 16C19.657 16 21 14.657 21 13H22H23V8L20.2754 2.55273C20.1064 2.21373 19.7618 2 19.3828 2H16V0H0ZM1 4V6H8V4H1ZM16 4H18.7637L19.7637 6H16V4ZM16 8H20.7637L21 8.47266V11H20.2207C19.6716 10.3907 18.8849 10 18 10C17.2279 10 16.5316 10.3002 16 10.7793V8ZM6 11.75C6.689 11.75 7.25 12.311 7.25 13C7.25 13.689 6.689 14.25 6 14.25C5.311 14.25 4.75 13.689 4.75 13C4.75 12.311 5.311 11.75 6 11.75ZM18 11.75C18.689 11.75 19.25 12.311 19.25 13C19.25 13.689 18.689 14.25 18 14.25C17.311 14.25 16.75 13.689 16.75 13C16.75 12.311 17.311 11.75 18 11.75Z" fill="#3FB993"/>
</svg>
  `;
  static ruby = `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.18582 7.24248L4.56934 8.45059L5.91469 9.48732L6.41544 9.97057L6.99554 9.48732L8.07639 7.91861L6.99554 5.75195L5.73432 5.81123L5.18582 7.24248Z" fill="#FF8888"/>
<path d="M8.8053 7.24219L8.34849 6.11169L6.99558 5.75166V9.48703L7.53068 9.97028L8.07643 9.48703L9.08515 8.67664L8.8053 7.24219ZM4.68727 11.1761L4.82858 12.94L2.84581 13.7097L0.28125 5.81094L2.52628 5.40283L3.22401 6.66964L4.68727 8.80002V11.1761Z" fill="#F60909"/>
<path d="M13.7097 5.81094L11.1452 13.7097L9.30371 12.9813V8.39399L10.767 6.66964L11.1452 5.40283L13.7097 5.81094Z" fill="#940505"/>
<path d="M6.99558 0.281738L8.14974 2.24883L6.99558 3.56415L6.05182 5.53248L3.22401 6.67032L0.28125 5.81162L6.99558 0.281738Z" fill="#FF8888"/>
<path d="M13.7094 5.81162L10.7667 6.67032L8.07597 5.75234L6.99512 3.56415V0.281738L13.7094 5.81162Z" fill="#F60909"/>
<path d="M5.91435 9.48788L6.05144 10.6157L4.68688 11.177L3.22363 6.67048L4.56899 6.4248L5.18548 7.24304L5.91435 9.48788Z" fill="#940505"/>
<path d="M10.7668 6.67048L9.30357 11.177L8.04004 10.6157L8.0761 9.48788L8.80497 7.24304L9.40344 6.4248L10.7668 6.67048Z" fill="#620404"/>
<path d="M6.9952 3.56445L8.03998 4.98259L6.9952 5.75264L5.18548 7.24317L3.22363 6.67062L6.9952 3.56445Z" fill="#F60909"/>
<path d="M10.7667 6.67062L8.80484 7.24317L6.99512 5.75264V3.56445L10.7667 6.67062Z" fill="#940505"/>
<path d="M5.91496 9.4873L4.6875 11.1764L5.76145 11.7429L6.99581 11.1764L8.0406 10.1692L6.99581 9.4873H5.91496Z" fill="#F60909"/>
<path d="M8.07597 9.4873H6.99512V11.1764L8.0399 11.7429L9.30343 11.1764L8.07597 9.4873Z" fill="#940505"/>
<path d="M4.68716 11.1763L2.8457 13.7099H6.99547L8.14963 12.4431L6.99547 11.1763H4.68716Z" fill="#FF8888"/>
<path d="M9.30343 11.1763H6.99512V13.7099H11.1449L9.30343 11.1763Z" fill="#F60909"/>
</svg>
  `;
  static money = `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20 10C19.9971 7.34871 18.9426 4.80684 17.0679 2.9321C15.1932 1.05736 12.6514 0.00286757 10.0002 0C9.77917 0 9.56721 0.0877974 9.41093 0.244078C9.25465 0.400358 9.16686 0.612319 9.16686 0.833333C9.16686 1.05435 9.25465 1.26631 9.41093 1.42259C9.56721 1.57887 9.77917 1.66667 10.0002 1.66667C11.5774 1.66656 13.1222 2.11407 14.4552 2.95718C15.7882 3.80029 16.8545 5.00438 17.5303 6.42952C18.206 7.85466 18.4635 9.44231 18.2727 11.008C18.0819 12.5736 17.4508 14.053 16.4525 15.2742L14.4526 13.2742L13.6018 19.2325L19.5592 18.3808L17.6317 16.4533C19.1648 14.6531 20.0046 12.3646 20 10Z" fill="#3FB993"/>
<path d="M10.0002 18.3333C8.42296 18.3334 6.87811 17.8859 5.54514 17.0428C4.21218 16.1997 3.14585 14.9956 2.47008 13.5705C1.79432 12.1453 1.53686 10.5577 1.72763 8.99202C1.91841 7.42636 2.54958 5.94699 3.5478 4.72583L5.54776 6.72583L6.39858 0.7675L0.441194 1.61917L2.36866 3.54667C1.13598 5.0016 0.345312 6.77885 0.0899889 8.66861C-0.165334 10.5584 0.12535 12.4817 0.927726 14.2116C1.7301 15.9415 3.01067 17.4057 4.61824 18.4314C6.22582 19.4571 8.09328 20.0013 10.0002 20C10.2212 20 10.4331 19.9122 10.5894 19.7559C10.7457 19.5996 10.8335 19.3877 10.8335 19.1667C10.8335 18.9457 10.7457 18.7337 10.5894 18.5774C10.4331 18.4211 10.2212 18.3333 10.0002 18.3333Z" fill="#3FB993"/>
<path d="M13.3193 11.845C13.3272 12.1602 13.265 12.4733 13.1372 12.7615C13.0094 13.0497 12.8191 13.306 12.5801 13.5117C11.9699 13.9961 11.2241 14.2789 10.446 14.3208V15.8333H9.51935V14.3617C8.54727 14.3793 7.58171 14.1995 6.68107 13.8333V12.3608C7.12994 12.573 7.59969 12.7377 8.08271 12.8525C8.55215 12.9758 9.03418 13.0449 9.51935 13.0583V10.45L8.93603 10.2333C8.30249 10.0238 7.72918 9.66378 7.26523 9.18417C6.90856 8.76375 6.72033 8.22608 6.7369 7.675C6.73085 7.37104 6.79544 7.06983 6.92559 6.79508C7.05573 6.52033 7.24787 6.27954 7.48689 6.09167C8.07498 5.64499 8.78291 5.38386 9.52018 5.34167V4.16667H10.4468V5.3175C11.3689 5.34542 12.2783 5.53906 13.1318 5.88917L12.6218 7.16667C11.9264 6.88696 11.193 6.71308 10.446 6.65083V9.18917L10.976 9.39167C11.6592 9.6093 12.2854 9.97622 12.8093 10.4658C13.1493 10.8434 13.3318 11.337 13.3193 11.845ZM8.37521 7.66083C8.36841 7.90566 8.45821 8.14333 8.6252 8.3225C8.88451 8.55731 9.18987 8.73554 9.52185 8.84583V6.67917C9.21111 6.70915 8.91551 6.82773 8.6702 7.02083C8.5766 7.09891 8.50155 7.19686 8.45053 7.30756C8.3995 7.41827 8.37377 7.53894 8.37521 7.66083ZM11.6735 11.9492C11.6772 11.8243 11.6539 11.7 11.6051 11.5849C11.5563 11.4699 11.4833 11.3667 11.391 11.2825C11.1103 11.0564 10.7898 10.8848 10.446 10.7767V13C11.2643 12.8761 11.6735 12.5258 11.6735 11.9492Z" fill="#3FB993"/>
</svg>
  `;
  static moneyGold = `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="10" cy="10" r="10" fill="#FFF7E6"/>
<path d="M13.4166 12.5139H3.31514C3.03893 12.5139 2.85742 12.282 2.85742 11.9414V10.7913C2.85742 10.4458 3.04288 10.2188 3.31514 10.2188H13.4166C13.6928 10.2188 13.8743 10.4507 13.8743 10.7913V11.9414C13.8743 12.282 13.6928 12.5139 13.4166 12.5139Z" fill="#FFE58F"/>
<path d="M10.8155 11.142V11.6374C10.8155 11.9364 10.6463 12.1328 10.3979 12.1328H2.85742C2.85742 12.4318 3.02659 12.6283 3.27494 12.6283H10.3943H12.4891C12.7411 12.6283 12.9066 12.4275 12.9066 12.1328V11.1377L10.8155 11.142Z" fill="#FFC53D"/>
<path d="M7.10111 12.1328V11.3854C7.10111 11.2359 7.01833 11.1377 6.89235 11.1377C6.76638 11.1377 6.68359 11.2359 6.68359 11.3854V12.1328" fill="#EE9733"/>
<path d="M8.35795 12.1328V11.3854C8.35795 11.2359 8.27516 11.1377 8.14919 11.1377C8.02321 11.1377 7.94043 11.2359 7.94043 11.3854V12.1328" fill="#EE9733"/>
<path d="M9.61381 12.1328V11.3854C9.61381 11.2359 9.53102 11.1377 9.40505 11.1377C9.27907 11.1377 9.19629 11.2359 9.19629 11.3854V12.1328" fill="#EE9733"/>
<path d="M10.8697 12.1328V11.3854C10.8697 11.2359 10.7869 11.1377 10.6609 11.1377C10.5349 11.1377 10.4521 11.2359 10.4521 11.3854V12.1328" fill="#EE9733"/>
<path d="M11.8087 10.219H4.69911C4.4523 10.219 4.28418 9.99197 4.28418 9.64645V8.49639C4.28418 8.15088 4.4523 7.92383 4.69911 7.92383H11.8087C12.059 7.92383 12.2236 8.15582 12.2236 8.49639V9.64645C12.2272 9.99197 12.059 10.219 11.8087 10.219Z" fill="#FFE58F"/>
<path d="M12.2422 8.53378V9.144C12.2422 9.51223 12.073 9.75421 11.8247 9.75421H4.28418C4.28418 10.1224 4.45335 10.3644 4.7017 10.3644H11.8211L12.4972 10.3642C12.7492 10.3642 12.9147 10.117 12.9147 9.75401V8.52832L12.2422 8.53378Z" fill="#FFC53D"/>
<path d="M8.83353 9.7589V8.83831C8.83353 8.65419 8.75075 8.5332 8.62477 8.5332C8.4988 8.5332 8.41602 8.65419 8.41602 8.83831V9.7589" fill="#EE9733"/>
<path d="M10.0894 9.7589V8.83831C10.0894 8.65419 10.0066 8.5332 9.88063 8.5332C9.75466 8.5332 9.67188 8.65419 9.67188 8.83831V9.7589" fill="#EE9733"/>
<path d="M11.3492 9.7589V8.83831C11.3492 8.65419 11.2664 8.5332 11.1404 8.5332C11.0144 8.5332 10.9316 8.65419 10.9316 8.83831V9.7589" fill="#EE9733"/>
<path d="M15.5606 14.2244C15.1523 13.9476 14.9306 13.4889 14.9306 12.9986C14.9306 11.1268 14.1763 9.34733 12.8217 8.02919L11.4698 6.71105V5.78836L12.5702 4.43858C12.6648 4.31468 12.727 4.16177 12.727 4.00887C12.727 3.63979 12.4134 3.33398 12.0348 3.33398H10.5586L10.3963 3.65034C10.3206 3.79533 10.1206 3.8296 10.0016 3.71361L9.61765 3.33398H8.38742C8.00889 3.33398 7.69524 3.63979 7.69524 4.00887C7.69524 4.16177 7.75743 4.31468 7.85206 4.43858L8.95251 5.78836V6.70842L7.60061 8.02655C6.24871 9.34469 5.49164 11.1242 5.49164 12.9959C5.49164 13.4863 5.27264 13.9476 4.86166 14.2218C4.45068 14.496 4.23438 14.96 4.23438 15.4503C4.23438 15.88 4.58046 16.2175 5.02118 16.2175H15.4038C15.8445 16.2175 16.1906 15.88 16.1906 15.4503C16.1906 14.96 15.9716 14.5012 15.5606 14.2244Z" fill="url(#paint0_linear_23835_165020)"/>
<path d="M11.629 6.70785H8.79543C8.54398 6.70785 8.32227 6.49431 8.32227 6.2465C8.32227 6.00133 8.54127 5.78516 8.79543 5.78516H11.6263C11.8778 5.78516 12.0995 5.99869 12.0995 6.2465C12.0995 6.49431 11.8805 6.70785 11.629 6.70785Z" fill="#D46B08"/>
<path d="M10.5284 6.70898H9.89844V9.16336H10.5284V6.70898Z" fill="#D46B08"/>
<path d="M10.134 14.1428C11.6691 14.1428 12.9135 12.9295 12.9135 11.4327C12.9135 9.93601 11.6691 8.72266 10.134 8.72266C8.59892 8.72266 7.35449 9.93601 7.35449 11.4327C7.35449 12.9295 8.59892 14.1428 10.134 14.1428Z" fill="#D46B08"/>
<path d="M10.7291 11.0002H9.57997C9.47452 11.0002 9.388 10.9185 9.388 10.8183C9.388 10.7208 9.47722 10.6364 9.57997 10.6364H11.305V9.91674H10.5398V9.56348H9.77194V9.91674H9.57997C9.05002 9.91674 8.62012 10.3201 8.62012 10.8183C8.62012 11.3166 9.05002 11.7199 9.57997 11.7199H10.7291C10.8345 11.7199 10.9211 11.8017 10.9211 11.9018C10.9211 11.9994 10.8318 12.0838 10.7291 12.0838H9.00406V12.8035H9.77194V13.1541H10.5398V12.8035H10.7291C11.259 12.8035 11.6889 12.4001 11.6889 11.9018C11.6916 11.4036 11.259 11.0002 10.7291 11.0002Z" fill="white"/>
<defs>
<linearGradient id="paint0_linear_23835_165020" x1="10.2125" y1="3.33398" x2="10.2125" y2="16.2175" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFC046"/>
<stop offset="1" stop-color="#FF9411"/>
</linearGradient>
</defs>
</svg>
`;
  static bgQr = `
<svg width="884" height="884" viewBox="0 0 884 884" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="323.567" y="-0.00195312" width="647.137" height="647.137" rx="173.903" transform="rotate(30 323.567 -0.00195312)" fill="#8EDFEB"/>
<rect x="535.349" y="2.84961" width="634.927" height="634.927" rx="173.903" transform="rotate(57 535.349 2.84961)" fill="#1C33FF"/>
</svg>`;

  static userIconAction = `
  <svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.9994 12.8257C13.876 13.3755 12.6128 13.7077 11.25 13.7077C9.88716 13.7077 8.62399 13.3755 7.50057 12.8257C3.55182 13.0414 0.416656 15.7805 0.416656 19.1243V20.6862L1.16957 20.8875C1.31474 20.9254 4.79116 21.8327 11.25 21.8327C17.7088 21.8327 21.1852 20.9254 21.3304 20.8875L22.0833 20.6862V19.1243C22.0833 15.7805 18.9482 13.0414 14.9994 12.8257Z" fill="blue"/>
<path d="M11.25 11.9021C14.9106 11.9021 17.75 8.50497 17.75 5.58268C17.75 2.59539 14.8347 0.166016 11.25 0.166016C7.66524 0.166016 4.74999 2.59539 4.74999 5.58268C4.74999 8.50497 7.58941 11.9021 11.25 11.9021Z" fill="blue"/>
</svg> `;
  static userIcon = `
<svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.9994 12.8257C13.876 13.3755 12.6128 13.7077 11.25 13.7077C9.88716 13.7077 8.62399 13.3755 7.50057 12.8257C3.55182 13.0414 0.416656 15.7805 0.416656 19.1243V20.6862L1.16957 20.8875C1.31474 20.9254 4.79116 21.8327 11.25 21.8327C17.7088 21.8327 21.1852 20.9254 21.3304 20.8875L22.0833 20.6862V19.1243C22.0833 15.7805 18.9482 13.0414 14.9994 12.8257Z" fill="#262626"/>
<path d="M11.25 11.9021C14.9106 11.9021 17.75 8.50497 17.75 5.58268C17.75 2.59539 14.8347 0.166016 11.25 0.166016C7.66524 0.166016 4.74999 2.59539 4.74999 5.58268C4.74999 8.50497 7.58941 11.9021 11.25 11.9021Z" fill="#262626"/>
</svg> `;

  static afiliate = `<svg width = "21" height = "20" viewBox = "0 0 21 20" fill = "none" xmlns = "http://www.w3.org/2000/svg" >
<path d="M20.5 10C20.4971 7.34871 19.4426 4.80684 17.5679 2.9321C15.6932 1.05736 13.1514 0.00286757 10.5002 0C10.2792 0 10.0672 0.0877974 9.91093 0.244078C9.75465 0.400358 9.66686 0.612319 9.66686 0.833333C9.66686 1.05435 9.75465 1.26631 9.91093 1.42259C10.0672 1.57887 10.2792 1.66667 10.5002 1.66667C12.0774 1.66656 13.6222 2.11407 14.9552 2.95718C16.2882 3.80029 17.3545 5.00438 18.0303 6.42952C18.706 7.85466 18.9635 9.44231 18.7727 11.008C18.5819 12.5736 17.9508 14.053 16.9525 15.2742L14.9526 13.2742L14.1018 19.2325L20.0592 18.3808L18.1317 16.4533C19.6648 14.6531 20.5046 12.3646 20.5 10Z" fill="#262626"/>
<path d="M10.5002 18.3333C8.92296 18.3334 7.37811 17.8859 6.04514 17.0428C4.71218 16.1997 3.64585 14.9956 2.97008 13.5705C2.29432 12.1453 2.03686 10.5577 2.22763 8.99202C2.41841 7.42636 3.04958 5.94699 4.0478 4.72583L6.04776 6.72583L6.89858 0.7675L0.941194 1.61917L2.86866 3.54667C1.63598 5.0016 0.845312 6.77885 0.589989 8.66861C0.334666 10.5584 0.62535 12.4817 1.42773 14.2116C2.2301 15.9415 3.51067 17.4057 5.11824 18.4314C6.72582 19.4571 8.59328 20.0013 10.5002 20C10.7212 20 10.9331 19.9122 11.0894 19.7559C11.2457 19.5996 11.3335 19.3877 11.3335 19.1667C11.3335 18.9457 11.2457 18.7337 11.0894 18.5774C10.9331 18.4211 10.7212 18.3333 10.5002 18.3333Z" fill="#262626"/>
<path d="M13.8193 11.845C13.8272 12.1602 13.765 12.4733 13.6372 12.7615C13.5094 13.0497 13.3191 13.306 13.0801 13.5117C12.4699 13.9961 11.7241 14.2789 10.946 14.3208V15.8333H10.0194V14.3617C9.04727 14.3793 8.08171 14.1995 7.18107 13.8333V12.3608C7.62994 12.573 8.09969 12.7377 8.58271 12.8525C9.05215 12.9758 9.53418 13.0449 10.0194 13.0583V10.45L9.43603 10.2333C8.80249 10.0238 8.22918 9.66378 7.76523 9.18417C7.40856 8.76375 7.22033 8.22608 7.2369 7.675C7.23085 7.37104 7.29544 7.06983 7.42559 6.79508C7.55573 6.52033 7.74787 6.27954 7.98689 6.09167C8.57498 5.64499 9.28291 5.38386 10.0202 5.34167V4.16667H10.9468V5.3175C11.8689 5.34542 12.7783 5.53906 13.6318 5.88917L13.1218 7.16667C12.4264 6.88696 11.693 6.71308 10.946 6.65083V9.18917L11.476 9.39167C12.1592 9.6093 12.7854 9.97622 13.3093 10.4658C13.6493 10.8434 13.8318 11.337 13.8193 11.845ZM8.87521 7.66083C8.86841 7.90566 8.95821 8.14333 9.1252 8.3225C9.38451 8.55731 9.68987 8.73554 10.0219 8.84583V6.67917C9.71111 6.70915 9.41551 6.82773 9.1702 7.02083C9.0766 7.09891 9.00155 7.19686 8.95053 7.30756C8.8995 7.41827 8.87377 7.53894 8.87521 7.66083ZM12.1735 11.9492C12.1772 11.8243 12.1539 11.7 12.1051 11.5849C12.0563 11.4699 11.9833 11.3667 11.891 11.2825C11.6103 11.0564 11.2898 10.8848 10.946 10.7767V13C11.7643 12.8761 12.1735 12.5258 12.1735 11.9492Z" fill="#262626"/>
</svg>
  `
  static afiliateAction = `<svg width = "21" height = "20" viewBox = "0 0 21 20" fill = "none" xmlns = "http://www.w3.org/2000/svg" >
  <path d="M20.5 10C20.4971 7.34871 19.4426 4.80684 17.5679 2.9321C15.6932 1.05736 13.1514 0.00286757 10.5002 0C10.2792 0 10.0672 0.0877974 9.91093 0.244078C9.75465 0.400358 9.66686 0.612319 9.66686 0.833333C9.66686 1.05435 9.75465 1.26631 9.91093 1.42259C10.0672 1.57887 10.2792 1.66667 10.5002 1.66667C12.0774 1.66656 13.6222 2.11407 14.9552 2.95718C16.2882 3.80029 17.3545 5.00438 18.0303 6.42952C18.706 7.85466 18.9635 9.44231 18.7727 11.008C18.5819 12.5736 17.9508 14.053 16.9525 15.2742L14.9526 13.2742L14.1018 19.2325L20.0592 18.3808L18.1317 16.4533C19.6648 14.6531 20.5046 12.3646 20.5 10Z" fill="blue"/>
  <path d="M10.5002 18.3333C8.92296 18.3334 7.37811 17.8859 6.04514 17.0428C4.71218 16.1997 3.64585 14.9956 2.97008 13.5705C2.29432 12.1453 2.03686 10.5577 2.22763 8.99202C2.41841 7.42636 3.04958 5.94699 4.0478 4.72583L6.04776 6.72583L6.89858 0.7675L0.941194 1.61917L2.86866 3.54667C1.63598 5.0016 0.845312 6.77885 0.589989 8.66861C0.334666 10.5584 0.62535 12.4817 1.42773 14.2116C2.2301 15.9415 3.51067 17.4057 5.11824 18.4314C6.72582 19.4571 8.59328 20.0013 10.5002 20C10.7212 20 10.9331 19.9122 11.0894 19.7559C11.2457 19.5996 11.3335 19.3877 11.3335 19.1667C11.3335 18.9457 11.2457 18.7337 11.0894 18.5774C10.9331 18.4211 10.7212 18.3333 10.5002 18.3333Z" fill="blue"/>
  <path d="M13.8193 11.845C13.8272 12.1602 13.765 12.4733 13.6372 12.7615C13.5094 13.0497 13.3191 13.306 13.0801 13.5117C12.4699 13.9961 11.7241 14.2789 10.946 14.3208V15.8333H10.0194V14.3617C9.04727 14.3793 8.08171 14.1995 7.18107 13.8333V12.3608C7.62994 12.573 8.09969 12.7377 8.58271 12.8525C9.05215 12.9758 9.53418 13.0449 10.0194 13.0583V10.45L9.43603 10.2333C8.80249 10.0238 8.22918 9.66378 7.76523 9.18417C7.40856 8.76375 7.22033 8.22608 7.2369 7.675C7.23085 7.37104 7.29544 7.06983 7.42559 6.79508C7.55573 6.52033 7.74787 6.27954 7.98689 6.09167C8.57498 5.64499 9.28291 5.38386 10.0202 5.34167V4.16667H10.9468V5.3175C11.8689 5.34542 12.7783 5.53906 13.6318 5.88917L13.1218 7.16667C12.4264 6.88696 11.693 6.71308 10.946 6.65083V9.18917L11.476 9.39167C12.1592 9.6093 12.7854 9.97622 13.3093 10.4658C13.6493 10.8434 13.8318 11.337 13.8193 11.845ZM8.87521 7.66083C8.86841 7.90566 8.95821 8.14333 9.1252 8.3225C9.38451 8.55731 9.68987 8.73554 10.0219 8.84583V6.67917C9.71111 6.70915 9.41551 6.82773 9.1702 7.02083C9.0766 7.09891 9.00155 7.19686 8.95053 7.30756C8.8995 7.41827 8.87377 7.53894 8.87521 7.66083ZM12.1735 11.9492C12.1772 11.8243 12.1539 11.7 12.1051 11.5849C12.0563 11.4699 11.9833 11.3667 11.891 11.2825C11.6103 11.0564 11.2898 10.8848 10.946 10.7767V13C11.7643 12.8761 12.1735 12.5258 12.1735 11.9492Z" fill="blue"/>
  </svg>
    `
  static shopAction = `<svg width="23" height="20" viewBox="0 0 23 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M17 6.92641C14.3417 6.92641 11.5 8.83117 11.5 12.1212C11.5 15.0649 15.9 19.3074 16.3583 19.7403C16.5417 19.9134 16.8167 20 17 20C17.1833 20 17.4583 19.9134 17.6417 19.7403C18.1 19.3074 22.5 15.0649 22.5 12.1212C22.5 8.83117 19.6583 6.92641 17 6.92641ZM17 13.8528C15.9917 13.8528 15.1667 13.0736 15.1667 12.1212C15.1667 11.1688 15.9917 10.3896 17 10.3896C18.0083 10.3896 18.8333 11.1688 18.8333 12.1212C18.8333 13.0736 18.0083 13.8528 17 13.8528Z" fill="#1C33FF"/>
<path d="M6 9.43723C6.55 10.0433 7.375 10.3896 8.29167 10.3896C8.93333 10.3896 9.48333 10.2164 10.0333 9.87013C11.0417 6.92641 14.0667 5.19481 17 5.19481C18.1 5.19481 19.2 5.45455 20.2083 5.88745L16.9083 0.4329C16.725 0.17316 16.45 0 16.0833 0H5.08333C4.71667 0 4.44167 0.17316 4.25833 0.4329L0.591667 6.49351C0.5 6.66667 0.5 6.75325 0.5 6.92641C0.5 9.00433 1.78333 10.3896 3.70833 10.3896C4.625 10.3896 5.45 10.0433 6 9.43723Z" fill="#1C33FF"/>
<path d="M9.66667 12.1212C9.66667 12.0346 9.66667 12.0346 9.66667 11.9481C9.20833 12.0346 8.75 12.1212 8.29167 12.1212C7.46667 12.1212 6.73333 11.9481 6 11.6017C5.26667 11.9481 4.53333 12.1212 3.70833 12.1212C3.525 12.1212 3.43333 12.1212 3.25 12.1212V18.1818C3.25 18.7013 3.61667 19.0476 4.16667 19.0476H13.2417C11.5917 17.2294 9.66667 14.4589 9.66667 12.1212Z" fill="#1C33FF"/>
</svg>
    `
  static shop = `<svg width="23" height="20" viewBox="0 0 23 20" fill="black" xmlns="http://www.w3.org/2000/svg">
<path d="M17 6.92641C14.3417 6.92641 11.5 8.83117 11.5 12.1212C11.5 15.0649 15.9 19.3074 16.3583 19.7403C16.5417 19.9134 16.8167 20 17 20C17.1833 20 17.4583 19.9134 17.6417 19.7403C18.1 19.3074 22.5 15.0649 22.5 12.1212C22.5 8.83117 19.6583 6.92641 17 6.92641ZM17 13.8528C15.9917 13.8528 15.1667 13.0736 15.1667 12.1212C15.1667 11.1688 15.9917 10.3896 17 10.3896C18.0083 10.3896 18.8333 11.1688 18.8333 12.1212C18.8333 13.0736 18.0083 13.8528 17 13.8528Z" fill="#262626"/>
<path d="M6 9.43723C6.55 10.0433 7.375 10.3896 8.29167 10.3896C8.93333 10.3896 9.48333 10.2164 10.0333 9.87013C11.0417 6.92641 14.0667 5.19481 17 5.19481C18.1 5.19481 19.2 5.45455 20.2083 5.88745L16.9083 0.4329C16.725 0.17316 16.45 0 16.0833 0H5.08333C4.71667 0 4.44167 0.17316 4.25833 0.4329L0.591667 6.49351C0.5 6.66667 0.5 6.75325 0.5 6.92641C0.5 9.00433 1.78333 10.3896 3.70833 10.3896C4.625 10.3896 5.45 10.0433 6 9.43723Z" fill="#262626"/>
<path d="M9.66667 12.1212C9.66667 12.0346 9.66667 12.0346 9.66667 11.9481C9.20833 12.0346 8.75 12.1212 8.29167 12.1212C7.46667 12.1212 6.73333 11.9481 6 11.6017C5.26667 11.9481 4.53333 12.1212 3.70833 12.1212C3.525 12.1212 3.43333 12.1212 3.25 12.1212V18.1818C3.25 18.7013 3.61667 19.0476 4.16667 19.0476H13.2417C11.5917 17.2294 9.66667 14.4589 9.66667 12.1212Z" fill="#262626"/>
</svg>
    `

  static wallet = `<svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0.5 4.16667C0.5 2.7856 1.61893 1.66667 3 1.66667H6.33333V3.33333H3C2.5394 3.33333 2.16667 3.70607 2.16667 4.16667C2.16667 4.62726 2.5394 5 3 5H3.83333V6.66667H3C1.61893 6.66667 0.5 5.54774 0.5 4.16667Z" fill="#262626"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.66667 0H18V6.66667H16.3333V1.66667H6.33333V6.66667H4.66667V0Z" fill="#262626"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.16667 4.16667C2.16667 4.62726 2.5394 5 3 5H20.5V20H3.83333C1.99226 20 0.5 18.5077 0.5 16.6667V4.16667H2.16667ZM3 6.66667C2.70778 6.66667 2.4273 6.61657 2.16667 6.5245V16.6667C2.16667 17.5873 2.91274 18.3333 3.83333 18.3333H18.8333V6.66667H3Z" fill="#262626"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.6667 11.6667C14.2064 11.6667 13.8333 12.0398 13.8333 12.5C13.8333 12.9602 14.2064 13.3333 14.6667 13.3333C15.1269 13.3333 15.5 12.9602 15.5 12.5C15.5 12.0398 15.1269 11.6667 14.6667 11.6667ZM12.1667 12.5C12.1667 11.1193 13.286 10 14.6667 10C16.0474 10 17.1667 11.1193 17.1667 12.5C17.1667 13.8807 16.0474 15 14.6667 15C13.286 15 12.1667 13.8807 12.1667 12.5Z" fill="#262626"/>
</svg>
        `
  static walletAction =
    `<svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.5 4.16667C0.5 2.7856 1.61893 1.66667 3 1.66667H6.33333V3.33333H3C2.5394 3.33333 2.16667 3.70607 2.16667 4.16667C2.16667 4.62726 2.5394 5 3 5H3.83333V6.66667H3C1.61893 6.66667 0.5 5.54774 0.5 4.16667Z" fill="blue"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.66667 0H18V6.66667H16.3333V1.66667H6.33333V6.66667H4.66667V0Z" fill="blue"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M2.16667 4.16667C2.16667 4.62726 2.5394 5 3 5H20.5V20H3.83333C1.99226 20 0.5 18.5077 0.5 16.6667V4.16667H2.16667ZM3 6.66667C2.70778 6.66667 2.4273 6.61657 2.16667 6.5245V16.6667C2.16667 17.5873 2.91274 18.3333 3.83333 18.3333H18.8333V6.66667H3Z" fill="blue"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.6667 11.6667C14.2064 11.6667 13.8333 12.0398 13.8333 12.5C13.8333 12.9602 14.2064 13.3333 14.6667 13.3333C15.1269 13.3333 15.5 12.9602 15.5 12.5C15.5 12.0398 15.1269 11.6667 14.6667 11.6667ZM12.1667 12.5C12.1667 11.1193 13.286 10 14.6667 10C16.0474 10 17.1667 11.1193 17.1667 12.5C17.1667 13.8807 16.0474 15 14.6667 15C13.286 15 12.1667 13.8807 12.1667 12.5Z" fill="blue"/>
        </svg>
                `
  static deliveryIcon =
    `     <svg width="23" height="17" viewBox="0 0 23 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M0 0.5V2.5H14V6.5V8.5V11.5H8.2207C7.67163 10.8907 6.88492 10.5 6 10.5C5.11508 10.5 4.32837 10.8907 3.7793 11.5H3V10.5L5 8.5H1V13.5H3C3 15.157 4.343 16.5 6 16.5C7.657 16.5 9 15.157 9 13.5H15C15 15.157 16.343 16.5 18 16.5C19.657 16.5 21 15.157 21 13.5H22H23V8.5L20.2754 3.05273C20.1064 2.71373 19.7618 2.5 19.3828 2.5H16V0.5H0ZM1 4.5V6.5H8V4.5H1ZM16 4.5H18.7637L19.7637 6.5H16V4.5ZM16 8.5H20.7637L21 8.97266V11.5H20.2207C19.6716 10.8907 18.8849 10.5 18 10.5C17.2279 10.5 16.5316 10.8002 16 11.2793V8.5ZM6 12.25C6.689 12.25 7.25 12.811 7.25 13.5C7.25 14.189 6.689 14.75 6 14.75C5.311 14.75 4.75 14.189 4.75 13.5C4.75 12.811 5.311 12.25 6 12.25ZM18 12.25C18.689 12.25 19.25 12.811 19.25 13.5C19.25 14.189 18.689 14.75 18 14.75C17.311 14.75 16.75 14.189 16.75 13.5C16.75 12.811 17.311 12.25 18 12.25Z" fill="#3FB993" />
    </svg>
    `
  static done =
    `     <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_25161_44660)">
<path d="M27.2703 -2.7002H-2.2002V27.2206H27.2703V-2.7002Z" fill="white"/>
<path d="M14.7653 24.4552C14.7653 24.4552 17.5494 23.0818 18.0282 22.3667C18.5069 21.6501 17.8038 21.6097 17.8038 21.6097C17.8038 21.6097 19.047 20.4009 18.8719 19.8923C18.6969 19.3836 18.0536 19.8504 18.0536 19.8504C18.0536 19.8504 18.8525 18.0716 18.4755 17.8935C17.7738 17.5599 16.7894 19.4734 16.7894 19.4734C16.7894 19.4734 17.0004 18.6416 16.5172 18.6685C15.7886 18.7089 15.8185 20.5894 15.8185 20.5894C15.8185 20.5894 15.7362 19.8683 15.2964 20.1032C14.5095 20.5236 14.7653 24.4552 14.7653 24.4552Z" fill="#E6E6E6"/>
<path d="M15.1391 19.8134C15.4817 19.6234 15.8243 20.18 15.8243 20.18C15.8243 20.18 15.7898 18.7108 16.5184 18.6704C17.0016 18.6435 16.7907 19.4753 16.7907 19.4753C16.7907 19.4753 17.7751 17.5619 18.4767 17.8955L14.7636 24.4272C14.7351 23.9679 14.4599 20.1904 15.1391 19.8134Z" fill="#F0F0F0"/>
<path d="M14.8369 24.4711C14.8369 24.4711 17.1139 24.7538 17.7033 24.5025C18.2928 24.2512 17.8709 23.9385 17.8709 23.9385C17.8709 23.9385 19.1365 23.6901 19.2353 23.3012C19.3325 22.9122 18.7491 22.9436 18.7491 22.9436C18.7491 22.9436 19.9623 22.1582 19.8008 21.8934C19.4986 21.4012 18.1073 22.1941 18.1073 22.1941C18.1073 22.1941 18.577 21.7617 18.2643 21.5822C17.7931 21.3114 17.0481 22.4963 17.0481 22.4963C17.0481 22.4963 17.0944 22.2046 16.7249 22.1717C16.0622 22.1133 14.8369 24.4711 14.8369 24.4711Z" fill="#E6E6E6"/>
<path d="M16.7101 21.9597C17.0796 21.9926 17.0482 22.4968 17.0482 22.4968C17.0482 22.4968 17.8067 21.092 18.2779 21.3628C18.5906 21.5423 18.2211 22.1317 18.2211 22.1317C18.2211 22.1317 19.4987 21.4017 19.8009 21.8939L14.8311 24.4596C15.0001 24.1634 16.0907 21.9058 16.7101 21.9597Z" fill="#F0F0F0"/>
<path d="M8.88426 24.3829C8.88426 24.3829 6.10013 23.0095 5.6214 22.2944C5.14266 21.5778 5.8458 21.5374 5.8458 21.5374C5.8458 21.5374 4.60259 20.3286 4.77763 19.82C4.95267 19.3113 5.60494 19.7975 5.60494 19.7975C5.60494 19.7975 4.79708 17.9993 5.17408 17.8213C5.87572 17.4877 6.86012 19.4011 6.86012 19.4011C6.86012 19.4011 6.64918 18.5693 7.1324 18.5962C7.86097 18.6366 7.83105 20.5171 7.83105 20.5171C7.83105 20.5171 7.91333 19.7961 8.35316 20.0309C9.14158 20.4513 8.88426 24.3829 8.88426 24.3829Z" fill="#E6E6E6"/>
<path d="M8.51149 19.7412C8.1689 19.5512 7.82631 20.1077 7.82631 20.1077C7.82631 20.1077 7.86071 18.6386 7.13214 18.5982C6.64892 18.5713 6.85986 19.4031 6.85986 19.4031C6.85986 19.4031 5.87547 17.4896 5.17383 17.8232L8.887 24.3519C8.91393 23.8957 9.19069 20.1182 8.51149 19.7412Z" fill="#F0F0F0"/>
<path d="M8.81243 24.3998C8.81243 24.3998 6.53545 24.6825 5.94602 24.4312C5.35658 24.1799 5.77846 23.8672 5.77846 23.8672C5.77846 23.8672 4.51281 23.6188 4.41407 23.2299C4.31683 22.8409 4.92123 22.8798 4.92123 22.8798C4.92123 22.8798 3.687 22.0869 3.84857 21.8221C4.15077 21.3299 5.54208 22.1228 5.54208 22.1228C5.54208 22.1228 5.07233 21.6905 5.385 21.5109C5.85625 21.2401 6.60128 22.425 6.60128 22.425C6.60128 22.425 6.5549 22.1333 6.92443 22.1004C7.58717 22.042 8.81243 24.3998 8.81243 24.3998Z" fill="#E6E6E6"/>
<path d="M6.88657 21.7052C6.51705 21.7381 6.52303 22.0957 6.52303 22.0957C6.52303 22.0957 5.84383 21.02 5.37258 21.2908C5.05991 21.4703 5.42943 22.0598 5.42943 22.0598C5.42943 22.0598 4.15181 21.3297 3.84961 21.8219L8.81945 24.3876C8.6504 24.0914 7.50443 21.6513 6.88657 21.7052Z" fill="#F0F0F0"/>
<path d="M16.6338 7.51562C16.6169 7.5987 16.6 7.68332 16.5569 7.75255C16.483 7.84178 16.4138 7.93409 16.3461 8.02947C16.3923 8.0264 16.4384 8.02486 16.4846 8.02486C16.9415 8.02486 17.3661 8.18486 17.68 8.47717C17.9615 8.73871 18.1169 9.07255 18.1169 9.41871C18.1169 9.76487 17.9615 10.0987 17.68 10.3603C17.3646 10.6526 16.94 10.8126 16.4846 10.8126C16.0292 10.8126 15.6061 10.6526 15.2907 10.3618C15.2677 10.4295 15.2446 10.4972 15.2215 10.5649C15.0677 10.6433 14.96 10.8264 14.96 11.0403C14.96 11.3249 15.1507 11.5556 15.3877 11.5556C15.5738 11.5556 15.7307 11.4126 15.7892 11.2126C16.0061 11.2849 16.24 11.3249 16.4846 11.3249C17.6215 11.3249 18.5415 10.471 18.5415 9.41717C18.5431 8.41101 17.7015 7.58639 16.6338 7.51562Z" fill="#E0973F"/>
<path d="M7.4077 7.51562C7.42462 7.5987 7.44155 7.68332 7.48462 7.75255C7.55847 7.84178 7.6277 7.93409 7.69539 8.02947C7.64924 8.0264 7.60309 8.02486 7.55693 8.02486C7.10001 8.02486 6.67539 8.18486 6.36154 8.47717C6.08 8.73871 5.92462 9.07255 5.92462 9.41871C5.92462 9.76487 6.08 10.0987 6.36154 10.3603C6.67693 10.6526 7.10155 10.8126 7.55693 10.8126C8.01232 10.8126 8.4354 10.6526 8.75078 10.3618C8.77386 10.4295 8.79694 10.4972 8.82001 10.5649C8.97386 10.6433 9.08155 10.8264 9.08155 11.0403C9.08155 11.3249 8.89078 11.5556 8.65386 11.5556C8.46771 11.5556 8.31078 11.4126 8.25232 11.2126C8.0354 11.2849 7.80155 11.3249 7.55693 11.3249C6.42 11.3249 5.5 10.471 5.5 9.41717C5.5 8.41101 6.34154 7.58639 7.4077 7.51562Z" fill="#E0973F"/>
<path d="M16.9304 7.29883V7.82498C16.9304 10.205 15.0012 12.1358 12.6196 12.1358C11.5227 12.1219 10.7704 12.2342 9.7304 11.7742C8.2304 11.1173 7.15039 9.60961 7.15039 7.82652V7.30037H16.9304V7.29883Z" fill="#F7CC7F"/>
<path d="M9.73123 11.7742C9.73277 11.7742 9.73277 11.7758 9.73431 11.7758L11.2835 7.29883H9.67123L8.42969 10.8881C8.80354 11.2588 9.24508 11.5604 9.73123 11.7742Z" fill="#FAE0B2"/>
<path d="M10.7382 12.0758L12.3736 7.29883H11.5628L9.99512 11.8819C10.2551 11.9773 10.4997 12.0373 10.7382 12.0758Z" fill="#FAE0B2"/>
<path d="M8.27062 10.7235L9.4937 7.29883H9.10909L8.00293 10.3973C8.08755 10.5112 8.17678 10.6188 8.27062 10.7235Z" fill="#FAE0B2"/>
<path d="M16.5028 7H7.60281C7.2782 7 7.01358 7.18154 6.99512 7.40923H17.1121C17.0921 7.18154 16.8275 7 16.5028 7Z" fill="#F7CC7F"/>
<path d="M10.3877 12.0088L10.8923 14.595H13.0939L13.7185 12.0088H10.3877Z" fill="#F7CC7F"/>
<path d="M13.7185 12.0088L13.5769 12.5919H10.5015L10.3877 12.0088H13.7185Z" fill="#B75742"/>
<path d="M13.7185 17.7926H10.3877L10.6385 16.5096L10.8923 15.208H13.0939L13.4092 16.5096L13.7185 17.7926Z" fill="#F7CC7F"/>
<path d="M13.7185 17.7928H10.3877L10.6385 16.5098H13.4092L13.7185 17.7928Z" fill="#B75742"/>
<path d="M13.4803 14.5957H10.5065C10.3757 14.5957 10.2695 14.7019 10.2695 14.8326V14.9695C10.2695 15.1003 10.3757 15.2065 10.5065 15.2065H13.4819C13.6126 15.2065 13.7188 15.1003 13.7188 14.9695V14.8326C13.7188 14.7019 13.6111 14.5957 13.4803 14.5957Z" fill="#FAE0B2"/>
<path d="M13.9626 17.6181H10.0303V16.9119C10.0303 16.8365 10.0918 16.7734 10.1687 16.7734H13.8257C13.9011 16.7734 13.9641 16.835 13.9641 16.9119V17.6181H13.9626Z" fill="#F7CC7F"/>
<path d="M15.2628 18.1223V18.3792H8.73047V18.1223C8.73047 17.8269 8.91047 17.5869 9.13355 17.5869H14.8597C15.0828 17.5854 15.2628 17.8254 15.2628 18.1223Z" fill="#F7CC7F"/>
<path d="M10.2691 17.5859L10.0614 18.3798H9.78906L9.99522 17.5859H10.2691Z" fill="#FAE0B2"/>
<path d="M9.87844 17.5859L9.67229 18.3798H9.39844L9.60613 17.5859H9.87844Z" fill="#FAE0B2"/>
<path d="M10.6148 17.5859L10.4086 18.3798H10.1348L10.3425 17.5859H10.6148Z" fill="#FAE0B2"/>
<path d="M17.6952 24.2463V24.5032H6.74902V24.2463C6.74902 23.9509 7.0521 23.7109 7.42441 23.7109H17.0198C17.3921 23.7109 17.6952 23.9509 17.6952 24.2463Z" fill="#1890FF"/>
<path d="M9.32554 23.7109L8.97939 24.5033H8.52246L8.86862 23.7109H9.32554Z" fill="white"/>
<path d="M8.67181 23.7109L8.32565 24.5033H7.86719L8.21488 23.7109H8.67181Z" fill="white"/>
<path d="M9.90757 23.7109L9.56142 24.5033H9.10449L9.45065 23.7109H9.90757Z" fill="white"/>
<path d="M14.8416 11.5215V11.7153C14.8416 12.0507 14.5693 12.323 14.2355 12.323H9.85086C9.51547 12.323 9.24316 12.0507 9.24316 11.7153V11.5215H14.8416Z" fill="#F7CC7F"/>
<path d="M16.177 23.7107H7.93848L8.04002 18.3799H16.0662L16.177 23.7107Z" fill="#1976CC"/>
<path d="M14.2169 23.7107H9.89844L9.95075 18.3799H14.16L14.2169 23.7107Z" fill="#1890FF"/>
<path d="M23.9951 24.148C24.028 24.148 24.055 24.1211 24.055 24.0882C24.055 24.0552 24.028 24.0283 23.9951 24.0283" fill="#184655"/>
<path d="M16.8174 18.3412C16.8174 18.6793 16.5197 18.965 16.421 19.2702C16.3177 19.5859 16.388 19.9928 16.1965 20.2561C16.0036 20.5224 15.5936 20.5808 15.3288 20.7738C15.0655 20.9652 14.8845 21.3378 14.5689 21.4395C14.2637 21.5382 13.9001 21.3482 13.562 21.3482C13.2239 21.3482 12.8604 21.5397 12.5552 21.4395C12.2395 21.3363 12.057 20.9652 11.7952 20.7738C11.5289 20.5808 11.1205 20.5209 10.9275 20.2561C10.736 19.9928 10.8048 19.5874 10.7031 19.2702C10.6044 18.965 10.3066 18.6793 10.3066 18.3412C10.3066 18.0031 10.6044 17.7173 10.7031 17.4122C10.8063 17.0965 10.736 16.6896 10.9275 16.4263C11.1205 16.16 11.5304 16.1016 11.7952 15.9086C12.0585 15.7171 12.2395 15.3446 12.5552 15.2429C12.8604 15.1442 13.2239 15.3342 13.562 15.3342C13.9001 15.3342 14.2637 15.1427 14.5689 15.2429C14.8845 15.3461 15.067 15.7171 15.3288 15.9086C15.5951 16.1016 16.0036 16.1615 16.1965 16.4263C16.388 16.6896 16.3192 17.095 16.421 17.4122C16.5197 17.7159 16.8174 18.0031 16.8174 18.3412Z" fill="#F47458"/>
<path d="M14.3714 21.2318C14.2697 21.2318 14.15 21.2049 14.0243 21.1779C13.8747 21.145 13.7221 21.1121 13.5605 21.1121C13.399 21.1121 13.2449 21.1465 13.0968 21.1779C12.9711 21.2064 12.8514 21.2318 12.7497 21.2318C12.7033 21.2318 12.6629 21.2258 12.627 21.2153C12.4969 21.1735 12.3802 21.0403 12.256 20.8997C12.1588 20.789 12.057 20.6738 11.9329 20.5825C11.8072 20.4913 11.6651 20.4284 11.5274 20.3701C11.3569 20.2953 11.1953 20.2265 11.116 20.1173C11.0382 20.011 11.0218 19.836 11.0038 19.652C10.9889 19.5024 10.9739 19.3483 10.926 19.1987C10.8796 19.0551 10.8018 18.9249 10.727 18.7992C10.6313 18.6377 10.54 18.4836 10.54 18.343C10.54 18.2023 10.6313 18.0482 10.727 17.8867C10.8018 17.761 10.8781 17.6308 10.926 17.4872C10.9754 17.3376 10.9903 17.1835 11.0038 17.0339C11.0218 16.8499 11.0397 16.6749 11.116 16.5687C11.1953 16.4594 11.3569 16.3906 11.5274 16.3158C11.6651 16.256 11.8072 16.1946 11.9329 16.1034C12.057 16.0121 12.1588 15.8984 12.256 15.7862C12.3802 15.6456 12.4969 15.5124 12.627 15.4706C12.6614 15.4586 12.7018 15.4541 12.7497 15.4541C12.8514 15.4541 12.9711 15.4795 13.0968 15.508C13.2464 15.5409 13.4005 15.5738 13.5605 15.5738C13.7221 15.5738 13.8762 15.5394 14.0243 15.508C14.15 15.4795 14.2697 15.4541 14.3714 15.4541C14.4178 15.4541 14.4582 15.4601 14.4941 15.4706C14.6242 15.5124 14.7409 15.6456 14.8651 15.7862C14.9623 15.8969 15.0641 16.0121 15.1882 16.1034C15.3139 16.1946 15.456 16.2575 15.5937 16.3158C15.7642 16.3906 15.9258 16.4594 16.0051 16.5687C16.0829 16.6749 16.0993 16.8499 16.1173 17.0339C16.1322 17.1835 16.1472 17.3376 16.1951 17.4872C16.2414 17.6308 16.3192 17.761 16.394 17.8867C16.4898 18.0482 16.581 18.2023 16.581 18.343C16.581 18.4851 16.4898 18.6377 16.394 18.7992C16.3192 18.9249 16.2429 19.0551 16.1951 19.1987C16.1457 19.3483 16.1307 19.5024 16.1173 19.652C16.0993 19.836 16.0814 20.011 16.0051 20.1173C15.9258 20.2265 15.7642 20.2953 15.5937 20.3701C15.456 20.4299 15.3139 20.4913 15.1882 20.5825C15.0641 20.6738 14.9623 20.7875 14.8651 20.8997C14.7409 21.0403 14.6242 21.1735 14.4941 21.2153C14.4597 21.2258 14.4193 21.2318 14.3714 21.2318Z" fill="#F7CC7F"/>
<path d="M15.9113 18.6696C16.0928 17.3718 15.1878 16.1725 13.89 15.991C12.5922 15.8096 11.3929 16.7145 11.2114 18.0124C11.0299 19.3102 11.9349 20.5094 13.2328 20.6909C14.5306 20.8724 15.7298 19.9674 15.9113 18.6696Z" fill="#F47458"/>
<path d="M13.5602 20.596C14.8053 20.596 15.8147 19.5866 15.8147 18.3414C15.8147 17.0963 14.8053 16.0869 13.5602 16.0869C12.3151 16.0869 11.3057 17.0963 11.3057 18.3414C11.3057 19.5866 12.3151 20.596 13.5602 20.596Z" fill="white"/>
<path d="M15.5935 18.5426C15.7046 17.4194 14.8842 16.4189 13.7611 16.3078C12.638 16.1967 11.6374 17.0171 11.5263 18.1403C11.4153 19.2634 12.2357 20.2639 13.3588 20.375C14.4819 20.4861 15.4824 19.6657 15.5935 18.5426Z" fill="#F7CC7F"/>
<path d="M7.15039 9.44187C7.17283 9.53313 7.21322 9.67825 7.21322 9.67825C7.21023 9.71864 7.19826 9.8847 7.1848 10.0867L7.48401 9.96698C7.48401 9.96698 7.48101 9.63636 7.49448 9.61541C7.50645 9.59896 7.5244 9.6199 7.61566 9.48825C7.64259 9.45085 7.64707 9.38353 7.64707 9.38353C7.64707 9.38353 7.65306 9.29975 7.63361 9.29227C7.61566 9.28479 7.59322 9.29676 7.58723 9.31172C7.57975 9.32668 7.58274 9.35959 7.57526 9.37455C7.56778 9.38951 7.5259 9.40597 7.50645 9.40597C7.48849 9.40447 7.50944 9.16062 7.50944 9.16062C7.50944 9.16062 7.51991 9.0918 7.5244 9.06786C7.52889 9.04842 7.54684 8.99157 7.54684 8.99157C7.54684 8.99157 7.56778 8.90928 7.54235 8.9033C7.52739 8.90031 7.48849 8.96613 7.46306 9.03794C7.43464 9.11424 7.41818 9.19652 7.4107 9.19503C7.39125 9.19204 7.40172 9.05739 7.40172 9.05739C7.40172 9.05739 7.42117 8.99605 7.41968 8.96613C7.43763 8.93023 7.4496 8.8644 7.41818 8.85842C7.4122 8.85692 7.37031 8.9407 7.37031 8.9407C7.37031 8.9407 7.35385 8.99905 7.34487 9.02747C7.32842 9.07684 7.32543 9.15314 7.31645 9.18755C7.30897 9.21597 7.30299 9.204 7.29999 9.2055C7.297 9.207 7.30149 9.1681 7.30598 9.11424C7.30897 9.08731 7.30448 9.0559 7.31346 9.02597C7.32094 8.99755 7.32393 8.95716 7.32692 8.93172C7.33141 8.86291 7.28803 8.86889 7.27307 8.93322C7.26708 8.95716 7.2611 8.98409 7.25511 9.01101C7.23118 9.11574 7.23267 9.21597 7.2207 9.21597C7.20574 9.21747 7.21622 9.03495 7.21622 9.03495C7.21622 9.03495 7.19377 8.93322 7.17133 9.02747C7.1459 9.13668 7.14889 9.22495 7.14889 9.22944C7.1474 9.25487 7.12795 9.34912 7.15039 9.44187Z" fill="#F8A492"/>
<path d="M10.3218 9.19969C10.2993 9.29094 10.2589 9.43606 10.2589 9.43606C10.2619 9.47645 10.2739 9.64102 10.2874 9.84448L9.98816 9.7248C9.98816 9.7248 9.99115 9.39417 9.97768 9.37323C9.96571 9.35677 9.94776 9.37771 9.8565 9.24606C9.82957 9.20866 9.82509 9.14134 9.82509 9.14134C9.82509 9.14134 9.8191 9.05756 9.83855 9.05008C9.8565 9.0426 9.87894 9.05457 9.88493 9.06953C9.89241 9.08449 9.88942 9.1174 9.8969 9.13236C9.90438 9.14732 9.94627 9.16378 9.96571 9.16378C9.98367 9.16229 9.96272 8.91843 9.96272 8.91843C9.96272 8.91843 9.95225 8.84961 9.94776 8.82568C9.94327 8.80623 9.92532 8.74938 9.92532 8.74938C9.92532 8.74938 9.90438 8.6671 9.92981 8.66111C9.94477 8.65812 9.98367 8.72395 10.0091 8.79576C10.0375 8.87205 10.054 8.95434 10.0615 8.95284C10.0809 8.94985 10.0704 8.8152 10.0704 8.8152C10.0704 8.8152 10.051 8.75387 10.0525 8.72395C10.0345 8.68804 10.0226 8.62221 10.054 8.61623C10.06 8.61473 10.1019 8.69851 10.1019 8.69851C10.1019 8.69851 10.1183 8.75686 10.1273 8.78528C10.1437 8.83465 10.1467 8.91095 10.1557 8.94536C10.1632 8.97378 10.1692 8.96182 10.1722 8.96331C10.1752 8.96481 10.1707 8.92591 10.1662 8.87205C10.1632 8.84513 10.1677 8.81371 10.1587 8.78379C10.1512 8.75536 10.1482 8.71497 10.1452 8.68954C10.1408 8.62072 10.1841 8.6267 10.1991 8.69103C10.2051 8.71497 10.2111 8.7419 10.217 8.76883C10.241 8.87355 10.2395 8.97378 10.2515 8.97378C10.2664 8.97528 10.2559 8.79276 10.2559 8.79276C10.2559 8.79276 10.2784 8.69103 10.3008 8.78528C10.3263 8.89449 10.3233 8.98276 10.3233 8.98725C10.3248 9.01268 10.3442 9.10843 10.3218 9.19969Z" fill="#F8A492"/>
<path d="M8.45806 10.2768C8.45806 10.2768 8.61066 10.1167 8.73184 10.3112C8.73184 10.3112 8.82609 10.2588 8.89192 10.3531C8.89192 10.3531 9.06246 10.3022 9.10286 10.5566C9.10286 10.5566 9.19112 10.5401 9.2016 10.6448C9.21356 10.7495 9.15971 10.8184 9.15971 10.8184C9.15971 10.8184 9.20908 10.8558 9.18664 10.9515C9.16719 11.0323 9.09987 11.0458 9.09987 11.0458C9.09987 11.0458 9.08191 11.1834 9.02207 11.2238C8.96223 11.2657 8.88144 11.2447 8.88144 11.2447C8.88144 11.2447 8.8605 11.3195 8.79019 11.3121C8.72137 11.3061 8.6346 11.2387 8.5568 11.2687C8.47901 11.2986 8.27704 11.2672 8.27704 11.2672C8.27704 11.2672 8.05114 11.1669 8.11847 10.9351C8.11847 10.9351 7.99878 10.8273 8.1035 10.6987C8.1035 10.6987 8.02721 10.4967 8.17831 10.3785C8.3324 10.2588 8.45806 10.2768 8.45806 10.2768Z" fill="#07345A"/>
<path d="M9.50551 16.9922C9.50551 16.9922 9.67157 17.1971 9.71197 17.2226C9.75386 17.248 9.69401 17.5457 9.58181 17.5652C9.51898 17.5756 9.48307 17.5068 9.48307 17.5068L9.50102 17.4216L9.26465 17.1777L9.50551 16.9922Z" fill="#F7A491"/>
<path d="M8.65145 17.043C8.65145 17.043 8.78909 17.2674 8.82799 17.2988C8.86539 17.3287 8.76814 17.6174 8.65445 17.6219C8.59012 17.6249 8.56468 17.5516 8.56468 17.5516L8.59311 17.4693L8.38965 17.1971L8.65145 17.043Z" fill="#F7A491"/>
<path d="M8.81818 17.29C8.81818 17.29 8.73889 17.3918 8.6955 17.4322C8.64015 17.4845 8.56834 17.5354 8.56834 17.5354C8.56834 17.5354 8.48755 17.682 8.46511 17.7344C8.41574 17.8481 8.36039 17.9752 8.36787 18.0396C8.37086 18.0605 8.41724 18.0814 8.45464 18.0695C8.61472 18.0201 9.02314 17.6461 9.0755 17.5459C9.13684 17.4292 8.81818 17.29 8.81818 17.29Z" fill="#07345A"/>
<path d="M7.65205 13.8657C7.66701 13.8238 7.68047 13.7849 7.69543 13.749C7.86598 13.7864 8.05748 13.8268 8.25495 13.8687L8.27889 14.5943C8.27889 14.5389 8.13078 15.7507 8.13078 15.7507C8.13078 15.7507 8.51526 16.602 8.77857 17.0972C8.67085 17.2318 8.61999 17.2752 8.4644 17.3664C8.05598 16.8772 7.52189 15.9826 7.50095 15.833C7.48599 15.7477 7.47402 14.3639 7.65205 13.8657Z" fill="#263238"/>
<path d="M9.17046 14.0756C9.1181 14.6411 9.0029 15.7197 9.01188 15.8065C9.0403 15.9142 9.55494 16.9136 9.6477 17.0602C9.57439 17.1709 9.46069 17.2906 9.33802 17.3235C9.31408 17.3295 8.46582 16.1222 8.41945 16.0264C8.36709 15.9187 8.28032 15.3263 8.27882 14.5947L8.25488 13.8691C8.58401 13.9395 8.88621 14.0083 9.17046 14.0756Z" fill="#263238"/>
<path d="M8.08817 11.501L8.47565 11.5548C8.47565 11.5548 8.78682 11.5623 8.82572 11.5623C8.86462 11.5623 9.10847 12.9776 9.17878 13.5102C9.2491 14.0427 9.19524 14.137 9.19524 14.137C9.19524 14.137 8.70304 14.2477 8.33951 14.2148C7.96101 14.1819 7.53613 13.9769 7.53613 13.9769C7.53613 13.9769 7.85928 12.9536 7.92361 12.635C7.98495 12.3133 8.08817 11.501 8.08817 11.501Z" fill="#1976CC"/>
<path d="M7.14755 9.78641C7.14755 9.78641 7.12212 10.8411 7.16251 10.9219C7.22086 11.0386 7.87163 11.8719 7.87163 11.8719C7.87163 11.8719 7.56046 13.1734 7.4303 13.6237C7.41684 13.6686 7.35849 13.9618 7.35849 13.9903C7.35849 14.0531 7.63376 14.1339 7.66668 14.1294C7.6966 14.1249 7.93596 12.8862 8.09005 12.2803C8.14242 12.0753 8.23068 11.8809 8.35336 11.7073L8.45958 11.5562L7.64124 10.7364C7.64124 10.7364 7.54101 9.82381 7.54101 9.74751C7.53951 9.72656 7.18794 9.7116 7.14755 9.78641Z" fill="#1890FF"/>
<path d="M8.81139 11.5585L9.83169 10.5187C9.83169 10.5187 9.97381 9.53585 9.98428 9.49845C9.99476 9.46254 10.2476 9.4431 10.3493 9.43262C10.3822 9.42963 10.3329 10.4799 10.2835 10.6564C10.2251 10.8643 9.45469 11.8113 9.45469 11.8113C9.45469 11.8113 9.31705 12.3245 9.34996 12.7015C9.40532 13.3268 9.55492 14.187 9.52799 14.214C9.36193 14.3187 8.72611 14.3292 8.68572 14.2648C8.2399 13.5482 8.81139 11.5585 8.81139 11.5585Z" fill="#1890FF"/>
<path d="M8.48315 11.3641L8.45622 11.5691C8.45622 11.5691 8.39489 11.8638 8.47418 11.9206C8.55347 11.9775 8.81228 11.5616 8.81228 11.5616L8.78535 11.0439L8.48315 11.3641Z" fill="#FFBE9D"/>
<path d="M8.79306 11.2105C8.72723 11.3871 8.57164 11.4963 8.46094 11.5382L8.48487 11.3646L8.78558 11.043L8.79306 11.2105Z" fill="#F47458"/>
<path d="M9.20633 10.691C9.20633 10.7702 9.16145 10.8331 9.10609 10.8331C9.05074 10.8331 9.00586 10.7687 9.00586 10.691C9.00586 10.6117 9.05074 10.5488 9.10609 10.5488C9.16145 10.5488 9.20633 10.6132 9.20633 10.691Z" fill="#07345A"/>
<path d="M8.78427 10.4673C8.85119 10.4673 8.90545 10.4305 8.90545 10.385C8.90545 10.3396 8.85119 10.3027 8.78427 10.3027C8.71734 10.3027 8.66309 10.3396 8.66309 10.385C8.66309 10.4305 8.71734 10.4673 8.78427 10.4673Z" fill="#07345A"/>
<path d="M8.59984 10.5001C8.43827 10.4448 8.26772 10.551 8.24229 10.7171C8.23181 10.7889 8.22134 10.8801 8.21984 10.9789C8.21536 11.2167 8.33953 11.4501 8.44724 11.4501C8.55496 11.4501 8.85417 11.296 8.86613 10.9564C8.87361 10.7485 8.76889 10.5585 8.59984 10.5001Z" fill="#FFBE9D"/>
<path d="M8.78711 10.9834C8.78711 11.0358 8.82152 11.0792 8.8649 11.0792C8.90829 11.0792 8.9427 11.0358 8.9427 10.9834C8.9427 10.9311 8.90829 10.8877 8.8649 10.8877C8.82152 10.8892 8.78711 10.9311 8.78711 10.9834Z" fill="#F7A491"/>
<path d="M8.12999 10.3812C8.01779 10.4695 7.97889 10.6041 8.04172 10.6819C8.10456 10.7597 8.24818 10.7507 8.35888 10.6624C8.47109 10.5742 8.50998 10.4395 8.44715 10.3617C8.38432 10.2824 8.24219 10.2914 8.12999 10.3812Z" fill="#07345A"/>
<path d="M8.52706 10.5234C8.58407 10.5234 8.63028 10.492 8.63028 10.4531C8.63028 10.4143 8.58407 10.3828 8.52706 10.3828C8.47004 10.3828 8.42383 10.4143 8.42383 10.4531C8.42383 10.492 8.47004 10.5234 8.52706 10.5234Z" fill="#07345A"/>
<path d="M8.11426 10.7499C8.11426 10.7993 8.15465 10.8397 8.20253 10.8397C8.25189 10.8397 8.29079 10.7993 8.29079 10.7499C8.29079 10.7005 8.2504 10.6602 8.20253 10.6602C8.15465 10.6602 8.11426 10.7005 8.11426 10.7499Z" fill="#07345A"/>
<path d="M9.7026 17.2158C9.7026 17.2158 9.63677 17.3265 9.59937 17.3714C9.55 17.4298 9.48567 17.4896 9.48567 17.4896C9.48567 17.4896 9.42434 17.6452 9.40938 17.699C9.37497 17.8187 9.33607 17.9623 9.35253 18.0252C9.35702 18.0446 9.4019 18.0835 9.4378 18.0596C9.57843 17.9683 9.95244 17.5405 9.99134 17.4342C10.0347 17.3131 9.7026 17.2158 9.7026 17.2158Z" fill="#07345A"/>
<path d="M17.3504 13.1504C17.3504 13.1504 16.8538 13.2308 16.4867 13.6156C16.1196 14.0004 15.9004 14.6195 15.9004 14.6195L16.6511 14.8504C16.6511 14.8521 16.3505 14.1141 17.3504 13.1504Z" fill="#FAE0B2"/>
<path d="M17.5236 13.5476L17.4692 13.1176L17.0821 12.9186C17.0379 12.8967 17.0396 12.8326 17.0838 12.8124L17.4777 12.6269L17.5474 12.2004C17.5558 12.1515 17.6153 12.1329 17.6509 12.17L17.9498 12.4853L18.3811 12.4195C18.4304 12.4128 18.466 12.4634 18.4423 12.5072L18.2317 12.8866L18.427 13.2727C18.449 13.3166 18.4117 13.3671 18.3624 13.3587L17.9345 13.2778L17.6238 13.583C17.5915 13.6184 17.5304 13.5965 17.5236 13.5476Z" fill="#F7CC7F"/>
<path d="M3.90039 15.5498C3.90039 15.5498 4.39701 15.6279 4.76408 16.0013C5.13115 16.3748 5.35039 16.9757 5.35039 16.9757L4.59965 17.1998C4.59798 17.1998 4.89862 16.4834 3.90039 15.5498Z" fill="#FAE0B2"/>
<path d="M3.47602 15.8979L3.53036 15.4677L3.91753 15.2686C3.96168 15.2467 3.95998 15.1826 3.91583 15.1624L3.52187 14.9768L3.45225 14.55C3.44376 14.5011 3.38263 14.4825 3.34867 14.5196L3.0498 14.8351L2.61849 14.7693C2.56924 14.7625 2.53358 14.8132 2.55735 14.857L2.76792 15.2366L2.57264 15.6229C2.55056 15.6668 2.58792 15.7174 2.63716 15.7089L3.06508 15.628L3.37584 15.9333C3.4098 15.9671 3.46923 15.9468 3.47602 15.8979Z" fill="#F7CC7F"/>
<path d="M20.8541 13.5241L20.972 13.3059L20.8743 13.0748C20.8625 13.0489 20.8844 13.0193 20.9097 13.0267L21.1352 13.0822L21.3052 12.9103C21.3237 12.8899 21.3557 12.9029 21.3591 12.9324L21.381 13.1857L21.5829 13.3096C21.6065 13.3244 21.6048 13.3614 21.5796 13.3725L21.3675 13.4723L21.322 13.7219C21.317 13.7497 21.285 13.7608 21.2665 13.7386L21.1133 13.5481L20.8844 13.5777C20.8592 13.5796 20.8407 13.5481 20.8541 13.5241Z" fill="#466EB6"/>
<path d="M13.0066 5.27062L13.1556 5.00052L13.032 4.71592C13.0184 4.6833 13.0455 4.64704 13.0777 4.6561L13.3639 4.72499L13.5789 4.51109C13.6026 4.48752 13.6433 4.50384 13.6466 4.53828L13.6737 4.85188L13.9294 5.00415C13.9582 5.02228 13.9565 5.06759 13.926 5.0821L13.6568 5.20717L13.5992 5.51534C13.5925 5.54978 13.5518 5.56247 13.5298 5.53528L13.3351 5.29962L13.0438 5.33769C13.0116 5.3395 12.9879 5.30144 13.0066 5.27062Z" fill="#F79C8D"/>
<path d="M20.3952 5L19.9943 5.27125L20.218 5.66675L19.8343 5.95375L20.0029 6.358L19.75 6.6205L19.8808 6.75L20.218 6.4L20.0597 6.01675L20.4572 5.71925L20.2369 5.329L20.6 5.08225L20.3952 5Z" fill="#F48C7F"/>
<path d="M15.003 0.535853L14.7998 0.989389L15.1858 1.21885L15.0047 1.67418L15.3466 1.94128L15.2637 2.30518L15.4381 2.35L15.5498 1.8624L15.2247 1.60964L15.4144 1.13459L15.0334 0.910514L15.218 0.5L15.003 0.535853Z" fill="#F48C7F"/>
<path d="M21.0498 11.4506L20.7645 11.0805L20.3823 11.3081L20.0849 10.9544L19.6906 11.1296L19.4226 10.9004L19.2998 11.0298L19.6595 11.3376L20.0313 11.1706L20.3408 11.539L20.7178 11.3147L20.9754 11.6504L21.0498 11.4506Z" fill="#F48C7F"/>
<path d="M7.71349 3.41227L7.8002 2.9308L7.41003 2.81L7.47661 2.33379L7.11586 2.16572L7.11431 1.7998H6.9502L6.95329 2.29002L7.29391 2.44934L7.22424 2.94656L7.60821 3.06386L7.52925 3.4998L7.71349 3.41227Z" fill="#F48C7F"/>
<path d="M4.03849 4L4.22143 4.33976L4.61466 4.31756L4.79076 4.71366L5.2028 4.56171L5.42335 5.00049L5.82 4.90658L6.10039 5.29415L5.95507 5.4L5.74477 5.10976L5.3259 5.20878L5.11389 4.78707L4.69502 4.94244L4.50011 4.50537L4.11543 4.52756L3.94617 4.21171L3.69655 4.27829L3.65039 4.10415L4.03849 4Z" fill="#F7CC7F"/>
<path d="M2.39825 6.73601L2.10023 6.76357L1.99855 6.32585L1.55678 6.31613L1.42355 6L1.25 6.06323L1.43056 6.48635L1.84779 6.49445L1.95297 6.95L2.4 6.90785L2.39825 6.73601Z" fill="#50BFA5"/>
<path d="M1.53218 11.75L1.42115 12.0342L0.950589 11.9011L0.737338 12.2968L0.370758 12.2554L0.349609 12.446L0.841319 12.5L1.044 12.1259L1.53218 12.2644L1.69961 11.8345L1.53218 11.75Z" fill="#50BFA5"/>
<path d="M2.56481 8.71091C2.88731 9.0214 2.89932 9.53431 2.58883 9.85681C2.27834 10.1793 1.76543 10.1913 1.44293 9.88082C1.12043 9.57033 1.10842 9.05742 1.41891 8.73492C1.7294 8.41242 2.24231 8.40042 2.56481 8.71091Z" fill="#FFF3D9"/>
<path d="M2.54811 9.38895L2.20331 9.4953L2.09524 9.8401C2.08666 9.86755 2.04892 9.87441 2.03348 9.85039L1.82592 9.55534L1.46568 9.55877C1.43652 9.55877 1.41937 9.52618 1.43652 9.50216L1.65266 9.21397L1.53773 8.87089C1.52915 8.84344 1.55488 8.81771 1.58233 8.82629L1.9237 8.94294L2.2136 8.72851C2.23762 8.71136 2.27021 8.72851 2.27021 8.75767L2.26507 9.11791L2.5584 9.32719C2.58242 9.34435 2.57556 9.38037 2.54811 9.38895Z" fill="#FED333"/>
<path d="M23.7504 10.0605C24.0729 10.371 24.0849 10.8839 23.7744 11.2064C23.4639 11.5289 22.951 11.5409 22.6285 11.2304C22.306 10.9199 22.294 10.407 22.6045 10.0845C22.915 9.76203 23.4261 9.75003 23.7504 10.0605Z" fill="#FFF3D9"/>
<path d="M23.7317 10.7386L23.3869 10.8449L23.2788 11.1897C23.2703 11.2172 23.2325 11.224 23.2171 11.2L23.0095 10.905L22.6493 10.9084C22.6201 10.9084 22.603 10.8758 22.6201 10.8518L22.8363 10.5636L22.7213 10.2205C22.7127 10.1931 22.7385 10.1673 22.7659 10.1759L23.1073 10.2925L23.3972 10.0781C23.4212 10.061 23.4538 10.0781 23.4538 10.1073L23.4487 10.4675L23.742 10.6768C23.766 10.694 23.7609 10.73 23.7317 10.7386Z" fill="#FED333"/>
<path d="M20.0707 9.0097L20.2193 9.23371C20.2354 9.25824 20.2749 9.25497 20.2856 9.22881L20.3913 8.98517L20.6707 8.92631C20.7012 8.91977 20.7101 8.8838 20.6868 8.86581L20.4647 8.69903L20.4898 8.43741C20.4934 8.40961 20.4593 8.38999 20.4325 8.40634L20.1907 8.54696L19.9256 8.44559C19.8969 8.43414 19.8665 8.45867 19.8754 8.48646L19.9471 8.73991L19.759 8.93776C19.7393 8.95901 19.7536 8.99335 19.7859 8.99499L20.0707 9.0097Z" fill="#F47458"/>
<path d="M19.5998 14.425C19.5998 14.5216 19.5326 14.6 19.4498 14.6C19.367 14.6 19.2998 14.5216 19.2998 14.425C19.2998 14.3284 19.367 14.25 19.4498 14.25C19.5326 14.25 19.5998 14.3284 19.5998 14.425Z" fill="#50BFA5"/>
<path d="M0.35 9.425C0.35 9.52162 0.272596 9.6 0.175 9.6C0.0790865 9.6 0 9.52162 0 9.425C0 9.32839 0.0774038 9.25 0.175 9.25C0.272596 9.25 0.35 9.32839 0.35 9.425Z" fill="#50BFA5"/>
<path d="M9.6722 1.5989L9.88835 1.64178C9.88835 1.64178 10.204 1.49426 10.228 1.42392C10.252 1.35359 10.2417 1.18891 10.2863 1.17862C10.3326 1.16833 10.4253 1.17176 10.4596 1.28154C10.4853 1.36045 10.3961 1.60747 10.4184 1.63149C10.4321 1.64521 10.5556 1.67438 10.6448 1.69325C10.7049 1.70525 10.7512 1.75157 10.7684 1.80989C10.7786 1.84935 10.7615 1.89223 10.7255 1.91282L10.6689 1.9437L10.6963 2.00374C10.7169 2.04834 10.698 2.0998 10.6534 2.12038L10.6088 2.14097L10.6277 2.17699C10.6534 2.22331 10.6328 2.28163 10.5848 2.30222L10.5454 2.31937L10.5488 2.32967C10.5608 2.37427 10.5385 2.42058 10.4973 2.43774L10.4579 2.45317L9.8832 2.33996C9.83002 2.32966 9.77856 2.30736 9.73396 2.27649L9.5384 2.20272C9.51782 2.19415 9.50581 2.17185 9.51095 2.14955L9.62074 1.64007C9.62417 1.6109 9.64819 1.59375 9.6722 1.5989Z" fill="#96D9C9"/>
<path d="M12.624 18.6692L12.8726 18.4206C12.8551 18.3616 12.847 18.2995 12.847 18.2359C12.847 17.8408 13.1689 17.5189 13.564 17.5189C13.9591 17.5189 14.281 17.8408 14.281 18.2359C14.281 18.631 13.9591 18.9529 13.564 18.9529C13.5002 18.9529 13.4381 18.945 13.3793 18.9273L13.1307 19.1759C13.2629 19.238 13.4096 19.2715 13.564 19.2715C14.1345 19.2715 14.5996 18.8061 14.5996 18.2359C14.5996 17.6656 14.1343 17.2003 13.564 17.2003C12.9938 17.2003 12.5284 17.6654 12.5284 18.2359C12.5284 18.3903 12.5619 18.537 12.624 18.6692Z" fill="white"/>
<path d="M13.5648 17.7577C13.8064 17.7577 14.0029 17.9632 14.0029 18.2158C14.0029 18.4684 13.8064 18.6738 13.5648 18.6738C13.3232 18.6738 13.1267 18.4684 13.1267 18.2158C13.1267 17.9632 13.3232 17.7577 13.5648 17.7577ZM13.5648 18.3683C13.6454 18.3683 13.7109 18.2998 13.7109 18.2155C13.7109 18.1315 13.6454 18.063 13.5648 18.063C13.4842 18.063 13.4187 18.1315 13.4187 18.2158C13.4189 18.2998 13.4844 18.3683 13.5648 18.3683Z" fill="white"/>
<path d="M13.3815 18.4171C13.4023 18.4171 13.3394 18.375 13.3815 18.4171C13.4132 18.4489 13.4132 18.5075 13.3815 18.5392L12.6676 19.2477C12.6359 19.2794 12.5844 19.2794 12.5528 19.2477C12.5211 19.2159 12.5211 19.1644 12.5528 19.1326L13.2597 18.4171C13.2755 18.4012 13.3415 18.377 13.3815 18.4171Z" fill="white"/>
<path d="M12.5487 18.9922L12.7479 18.9922C12.7809 18.9922 12.8076 19.0189 12.8076 19.0519L12.8076 19.2511C12.8076 19.2668 12.8013 19.2821 12.7902 19.2934L12.5512 19.5323C12.5341 19.5494 12.5084 19.5545 12.4861 19.5452C12.4637 19.536 12.4492 19.5141 12.4492 19.49L12.4492 19.3506L12.3097 19.3506C12.2856 19.3506 12.2638 19.336 12.2545 19.3137C12.2453 19.2913 12.2503 19.2656 12.2674 19.2486L12.5064 19.0096C12.5176 18.9985 12.5328 18.9922 12.5487 18.9922Z" fill="white"/>
</g>
<defs>
<clipPath id="clip0_25161_44660">
<rect width="24" height="24" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>
`
  static cancel =
    ` <svg width="18" height="21" viewBox="0 0 18 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M17.0297 5.26406L11.9859 0.220313C11.8453 0.0796875 11.6555 0 11.4562 0H1.5C1.08516 0 0.75 0.335156 0.75 0.75V20.25C0.75 20.6648 1.08516 21 1.5 21H16.5C16.9148 21 17.25 20.6648 17.25 20.25V5.79609C17.25 5.59687 17.1703 5.40469 17.0297 5.26406ZM15.5203 6.14062H11.1094V1.72969L15.5203 6.14062ZM15.5625 19.3125H2.4375V1.6875H9.51562V6.75C9.51562 7.01107 9.61934 7.26145 9.80394 7.44606C9.98855 7.63066 10.2389 7.73438 10.5 7.73438H15.5625V19.3125ZM9.04922 12.0961L7.60078 9.69609C7.54922 9.61172 7.45781 9.56016 7.35938 9.56016H6.45938C6.40547 9.56016 6.35391 9.57422 6.30938 9.60469C6.17813 9.68672 6.13828 9.86016 6.22266 9.99375L8.15156 13.05L6.19687 16.1625C6.17032 16.2052 6.15565 16.2542 6.15438 16.3045C6.15312 16.3547 6.1653 16.4044 6.18968 16.4484C6.21405 16.4923 6.24973 16.529 6.29303 16.5546C6.33632 16.5801 6.38566 16.5937 6.43594 16.5938H7.24453C7.34297 16.5938 7.43203 16.5422 7.48359 16.4602L8.95312 14.0812L10.4133 16.4578C10.4648 16.5422 10.5562 16.5914 10.6523 16.5914H11.5312C11.5852 16.5914 11.6367 16.575 11.6836 16.5469C11.8148 16.4625 11.8523 16.2891 11.768 16.1578L9.79922 13.1016L11.7984 9.99609C11.8255 9.95353 11.8407 9.90449 11.8423 9.85408C11.844 9.80367 11.8322 9.75373 11.808 9.70946C11.7838 9.66519 11.7482 9.62821 11.7049 9.60236C11.6616 9.57652 11.6122 9.56275 11.5617 9.5625H10.725C10.6266 9.5625 10.5352 9.61406 10.4836 9.69844L9.04922 12.0961Z" fill="#FFA940"/>
</svg>
    `
  static star =
    ` <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.01113 0.962891L5.79863 5.44648L0.848633 6.166L4.42988 9.65818L3.58379 14.5871L8.01113 12.2597L12.4385 14.5847L11.5924 9.65584L15.1736 6.166L10.2236 5.44648L8.01113 0.962891Z" fill="#FFC043"/>
</svg>
    `
  static micro =
    ` <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.00033 1.44412C4.80154 1.44412 3.86699 2.41761 3.86699 3.66634V5.88856C3.86699 7.13729 4.80154 8.11079 6.00033 8.11079C7.19911 8.11079 8.13366 7.13729 8.13366 5.88856V3.66634C8.13366 2.41761 7.19911 1.44412 6.00033 1.44412ZM2.80033 3.66634C2.80033 1.80396 4.21244 0.333008 6.00033 0.333008C7.78821 0.333008 9.20033 1.80396 9.20033 3.66634V5.88856C9.20033 7.75094 7.78821 9.2219 6.00033 9.2219C4.21244 9.2219 2.80033 7.75094 2.80033 5.88856V3.66634Z" fill="#E8E8E8"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M1.73366 5.33301V5.88856C1.73366 8.35952 3.62821 10.333 6.00033 10.333C8.37244 10.333 10.267 8.35952 10.267 5.88856V5.33301H11.3337V5.88856C11.3337 8.97317 8.96154 11.4441 6.00033 11.4441C3.03911 11.4441 0.666992 8.97317 0.666992 5.88856V5.33301H1.73366Z" fill="#E8E8E8"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.80033 12.5552H9.20033V13.6663H2.80033V12.5552Z" fill="#E8E8E8"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.53366 10.333V13.6663H5.46699V10.333H6.53366Z" fill="#E8E8E8"/>
</svg>
    `
  static listProduct =
    ` <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6.375 6.90625H13.875C13.9438 6.90625 14 6.85 14 6.78125V5.90625C14 5.8375 13.9438 5.78125 13.875 5.78125H6.375C6.30625 5.78125 6.25 5.8375 6.25 5.90625V6.78125C6.25 6.85 6.30625 6.90625 6.375 6.90625ZM6.25 10.0938C6.25 10.1625 6.30625 10.2188 6.375 10.2188H13.875C13.9438 10.2188 14 10.1625 14 10.0938V9.21875C14 9.15 13.9438 9.09375 13.875 9.09375H6.375C6.30625 9.09375 6.25 9.15 6.25 9.21875V10.0938ZM14.125 2.5H1.875C1.80625 2.5 1.75 2.55625 1.75 2.625V3.5C1.75 3.56875 1.80625 3.625 1.875 3.625H14.125C14.1938 3.625 14.25 3.56875 14.25 3.5V2.625C14.25 2.55625 14.1938 2.5 14.125 2.5ZM14.125 12.375H1.875C1.80625 12.375 1.75 12.4312 1.75 12.5V13.375C1.75 13.4438 1.80625 13.5 1.875 13.5H14.125C14.1938 13.5 14.25 13.4438 14.25 13.375V12.5C14.25 12.4312 14.1938 12.375 14.125 12.375ZM2.225 10.0328L4.66719 8.10938C4.68363 8.09645 4.69692 8.07996 4.70606 8.06115C4.71521 8.04234 4.71995 8.0217 4.71995 8.00078C4.71995 7.97987 4.71521 7.95922 4.70606 7.94041C4.69692 7.9216 4.68363 7.90511 4.66719 7.89219L2.225 5.96719C2.13437 5.89531 2 5.95937 2 6.075V9.92344C1.99999 9.94953 2.00733 9.97511 2.02117 9.99723C2.03501 10.0194 2.05479 10.0371 2.07827 10.0486C2.10174 10.06 2.12794 10.0645 2.15389 10.0618C2.17984 10.059 2.20448 10.0489 2.225 10.0328Z" fill="black"/>
</svg>
    `
  static label =
    ` <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.9873 2.99766C13.9826 1.71484 12.942 0.675781 11.6576 0.675781H7.99824L8.33574 2.00703L11.4764 2.68984C11.6181 2.7228 11.7451 2.80131 11.838 2.91335C11.9308 3.02538 11.9844 3.16477 11.9904 3.31016C11.992 3.31797 11.992 6.68672 11.9904 6.69453C11.9844 6.83991 11.9308 6.9793 11.838 7.09134C11.7451 7.20338 11.6181 7.28189 11.4764 7.31484L8.33574 7.99766L7.99824 9.33047H11.6576C12.9404 9.33047 13.9826 8.29141 13.9873 7.00859V2.99766ZM2.52324 7.31797C2.38151 7.28502 2.25449 7.2065 2.16165 7.09447C2.0688 6.98243 2.01524 6.84304 2.00918 6.69766C2.00762 6.68828 2.00762 3.32109 2.00918 3.31328C2.02168 3.01016 2.2373 2.75859 2.52324 2.69297L5.66387 2.01016L6.00137 0.678906H2.34043C1.05762 0.678906 0.0154297 1.71641 0.0107422 3.00078V7.01484C0.0154297 8.29766 1.05762 9.33672 2.34043 9.33672H5.9998L5.6623 8.00391L2.52324 7.31797ZM5.6623 4.83672H8.33418V5.16953H5.6623V4.83672Z" fill="black"/>
</svg>
    `
  static price =
    ` <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.83028 7.73394V8.41701C3.83028 9.14166 4.10662 9.83663 4.59852 10.349C5.09042 10.8614 5.75758 11.1493 6.45323 11.1493H7.10896V12.5155H6.45323C5.40975 12.5155 4.40902 12.0837 3.67117 11.315C2.93332 10.5464 2.5188 9.50399 2.5188 8.41701V7.73394H3.83028Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.17454 1.70013C2.76874 1.70013 2.37957 1.86805 2.09263 2.16695C1.80568 2.46586 1.64448 2.87126 1.64448 3.29397C1.64448 3.42865 1.7125 3.67298 1.90167 4.02297C2.08094 4.35465 2.32858 4.70843 2.58943 5.04099C2.79418 5.30202 3.00004 5.54111 3.17454 5.73513C3.34904 5.54111 3.55489 5.30202 3.75965 5.04099C4.0205 4.70843 4.26813 4.35465 4.4474 4.02297C4.63657 3.67298 4.70459 3.42865 4.70459 3.29397C4.70459 2.87126 4.54339 2.46586 4.25645 2.16695C3.96951 1.86805 3.58033 1.70013 3.17454 1.70013ZM3.17454 6.70933C2.72346 7.20512 2.7233 7.20496 2.7233 7.20496L2.72078 7.20246L2.71497 7.19669L2.69436 7.17604C2.67676 7.15832 2.65159 7.13281 2.61994 7.10026C2.55667 7.03519 2.46725 6.94176 2.36038 6.826C2.14733 6.59524 1.86147 6.27207 1.57385 5.90538C1.28826 5.54128 0.989443 5.1198 0.758878 4.69321C0.538213 4.28495 0.333008 3.788 0.333008 3.29397C0.333008 2.50893 0.632382 1.75605 1.16527 1.20094C1.69816 0.645839 2.42092 0.333984 3.17454 0.333984C3.92816 0.333984 4.65091 0.645839 5.1838 1.20094C5.71669 1.75605 6.01607 2.50893 6.01607 3.29397C6.01607 3.788 5.81086 4.28495 5.5902 4.69321C5.35963 5.1198 5.06082 5.54128 4.77522 5.90538C4.4876 6.27207 4.20175 6.59524 3.9887 6.826C3.88182 6.94176 3.79241 7.03519 3.72914 7.10026C3.69748 7.13281 3.67231 7.15832 3.65471 7.17604L3.6341 7.19669L3.62829 7.20246L3.62598 7.20475C3.62598 7.20475 3.62561 7.20512 3.17454 6.70933ZM3.17454 6.70933L3.62598 7.20475L3.17454 7.65044L2.7233 7.20496L3.17454 6.70933Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.73192 4.54627C9.03627 4.54627 8.36911 4.83413 7.87721 5.34654C7.38531 5.85894 7.10896 6.55391 7.10896 7.27856C7.10896 7.57497 7.24821 8.02471 7.54304 8.59581C7.82742 9.14666 8.21443 9.73152 8.61338 10.2748C9.01048 10.8155 9.40915 11.3011 9.70935 11.6523C9.71694 11.6612 9.72446 11.67 9.73192 11.6787C9.73937 11.67 9.74689 11.6612 9.75448 11.6523C10.0547 11.3011 10.4534 10.8155 10.8504 10.2748C11.2494 9.73152 11.6364 9.14666 11.9208 8.59581C12.2156 8.02471 12.3549 7.57497 12.3549 7.27856C12.3549 6.55391 12.0785 5.85894 11.5866 5.34654C11.0947 4.83413 10.4276 4.54627 9.73192 4.54627ZM9.73192 12.6862C9.26122 13.1618 9.26106 13.1616 9.26106 13.1616L9.25787 13.1582L9.24985 13.1495L9.22038 13.1174C9.19496 13.0897 9.15825 13.0493 9.11186 12.9975C9.01911 12.8941 8.88748 12.7452 8.72989 12.5608C8.41534 12.1928 7.99434 11.6802 7.57176 11.1048C7.15104 10.532 6.71838 9.88281 6.388 9.24286C6.06808 8.62318 5.79749 7.92536 5.79749 7.27856C5.79749 6.19159 6.21201 5.14913 6.94986 4.38053C7.6877 3.61192 8.68844 3.18012 9.73192 3.18012C10.7754 3.18012 11.7761 3.61192 12.514 4.38053C13.2518 5.14913 13.6663 6.19159 13.6663 7.27856C13.6663 7.92536 13.3957 8.62318 13.0758 9.24286C12.7455 9.88281 12.3128 10.532 11.8921 11.1048C11.4695 11.6802 11.0485 12.1928 10.7339 12.5608C10.5764 12.7452 10.4447 12.8941 10.352 12.9975C10.3056 13.0493 10.2689 13.0897 10.2435 13.1174L10.214 13.1495L10.206 13.1582L10.203 13.1614C10.203 13.1614 10.2026 13.1618 9.73192 12.6862ZM9.73192 12.6862L10.203 13.1614L9.73192 13.6673L9.26106 13.1616L9.73192 12.6862Z" fill="black"/>
<path d="M3.17454 3.86319C3.47633 3.86319 3.72099 3.60834 3.72099 3.29397C3.72099 2.97959 3.47633 2.72474 3.17454 2.72474C2.87274 2.72474 2.62809 2.97959 2.62809 3.29397C2.62809 3.60834 2.87274 3.86319 3.17454 3.86319Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.73192 6.82318C9.49048 6.82318 9.29476 7.02706 9.29476 7.27856C9.29476 7.53006 9.49048 7.73394 9.73192 7.73394C9.97335 7.73394 10.1691 7.53006 10.1691 7.27856C10.1691 7.02706 9.97335 6.82318 9.73192 6.82318ZM7.98328 7.27856C7.98328 6.27256 8.76617 5.45703 9.73192 5.45703C10.6977 5.45703 11.4805 6.27256 11.4805 7.27856C11.4805 8.28456 10.6977 9.10009 9.73192 9.10009C8.76617 9.10009 7.98328 8.28456 7.98328 7.27856Z" fill="black"/>
</svg>
`

  static instock =
    ` <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10 0L20 5.96756L10 11.9351L0 5.96756L10 0ZM3.78253 5.96756L10 9.67787L16.2175 5.96756L10 2.25725L3.78253 5.96756Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M1.55751 8.67221L10 13.7103L18.4425 8.67221L19.4437 10.3319L10 15.9676L0.556254 10.3319L1.55751 8.67221Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M1.55751 12.7047L10 17.7428L18.4425 12.7047L19.4437 14.3644L10 20L0.556254 14.3644L1.55751 12.7047Z" fill="black"/>
</svg>
`
  static like =
    ` <svg width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20.6328 4.64689C20.3187 3.91948 19.8657 3.2603 19.2992 2.70627C18.7323 2.15058 18.064 1.70898 17.3305 1.40549C16.5699 1.08953 15.7541 0.927808 14.9305 0.929704C13.775 0.929704 12.6477 1.24611 11.668 1.84377C11.4336 1.98674 11.2109 2.14377 11 2.31486C10.7891 2.14377 10.5664 1.98674 10.332 1.84377C9.35234 1.24611 8.225 0.929704 7.06953 0.929704C6.2375 0.929704 5.43125 1.08908 4.66953 1.40549C3.93359 1.71017 3.27031 2.14845 2.70078 2.70627C2.13359 3.25968 1.6805 3.91901 1.36719 4.64689C1.04141 5.40392 0.875 6.20783 0.875 7.03517C0.875 7.81564 1.03438 8.62892 1.35078 9.45627C1.61563 10.1477 1.99531 10.8649 2.48047 11.5891C3.24922 12.7352 4.30625 13.9305 5.61875 15.1422C7.79375 17.1508 9.94766 18.5383 10.0391 18.5945L10.5945 18.9508C10.8406 19.1078 11.157 19.1078 11.4031 18.9508L11.9586 18.5945C12.05 18.536 14.2016 17.1508 16.3789 15.1422C17.6914 13.9305 18.7484 12.7352 19.5172 11.5891C20.0023 10.8649 20.3844 10.1477 20.6469 9.45627C20.9633 8.62892 21.1227 7.81564 21.1227 7.03517C21.125 6.20783 20.9586 5.40392 20.6328 4.64689Z" fill="black"/>
</svg>
`

  static violent =
    `  <svg width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.3988 18.0625L11.6488 1.1875C11.5035 0.936719 11.2527 0.8125 10.9996 0.8125C10.7465 0.8125 10.4934 0.936719 10.3504 1.1875L0.600399 18.0625C0.312117 18.5641 0.673055 19.1875 1.24962 19.1875H20.7496C21.3262 19.1875 21.6871 18.5641 21.3988 18.0625ZM10.2496 7.75C10.2496 7.64687 10.334 7.5625 10.4371 7.5625H11.5621C11.6652 7.5625 11.7496 7.64687 11.7496 7.75V12.0625C11.7496 12.1656 11.6652 12.25 11.5621 12.25H10.4371C10.334 12.25 10.2496 12.1656 10.2496 12.0625V7.75ZM10.9996 16C10.7052 15.994 10.4249 15.8728 10.2188 15.6625C10.0128 15.4522 9.89735 15.1695 9.89735 14.875C9.89735 14.5805 10.0128 14.2978 10.2188 14.0875C10.4249 13.8772 10.7052 13.756 10.9996 13.75C11.294 13.756 11.5743 13.8772 11.7804 14.0875C11.9865 14.2978 12.1019 14.5805 12.1019 14.875C12.1019 15.1695 11.9865 15.4522 11.7804 15.6625C11.5743 15.8728 11.294 15.994 10.9996 16Z" fill="black"/>
</svg>
`

  static productReviewAction =
    ` <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.625 5.26562H15.1656C15.4844 4.76406 15.6719 4.16875 15.6719 3.53125C15.6719 1.74766 14.2211 0.296875 12.4375 0.296875C11.4672 0.296875 10.593 0.728125 10 1.40781C9.40703 0.728125 8.53281 0.296875 7.5625 0.296875C5.77891 0.296875 4.32812 1.74766 4.32812 3.53125C4.32812 4.16875 4.51328 4.76406 4.83438 5.26562H1.375C0.960156 5.26562 0.625 5.60078 0.625 6.01562V10.7031C0.625 10.8062 0.709375 10.8906 0.8125 10.8906H1.75V18.9531C1.75 19.368 2.08516 19.7031 2.5 19.7031H17.5C17.9148 19.7031 18.25 19.368 18.25 18.9531V10.8906H19.1875C19.2906 10.8906 19.375 10.8062 19.375 10.7031V6.01562C19.375 5.60078 19.0398 5.26562 18.625 5.26562ZM10.7969 3.53125C10.7969 2.62656 11.5328 1.89062 12.4375 1.89062C13.3422 1.89062 14.0781 2.62656 14.0781 3.53125C14.0781 4.43594 13.3422 5.17188 12.4375 5.17188H10.7969V3.53125ZM7.5625 1.89062C8.46719 1.89062 9.20312 2.62656 9.20312 3.53125V5.17188H7.5625C6.65781 5.17188 5.92188 4.43594 5.92188 3.53125C5.92188 2.62656 6.65781 1.89062 7.5625 1.89062ZM2.21875 9.29688V6.85938H9.20312V9.29688H2.21875ZM3.34375 10.8906H9.20312V18.1094H3.34375V10.8906ZM16.6562 18.1094H10.7969V10.8906H16.6562V18.1094ZM17.7812 9.29688H10.7969V6.85938H17.7812V9.29688Z" fill="#1C33FF"/>
</svg>
`
  static productReview =
    ` <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.625 5.26562H15.1656C15.4844 4.76406 15.6719 4.16875 15.6719 3.53125C15.6719 1.74766 14.2211 0.296875 12.4375 0.296875C11.4672 0.296875 10.593 0.728125 10 1.40781C9.40703 0.728125 8.53281 0.296875 7.5625 0.296875C5.77891 0.296875 4.32812 1.74766 4.32812 3.53125C4.32812 4.16875 4.51328 4.76406 4.83438 5.26562H1.375C0.960156 5.26562 0.625 5.60078 0.625 6.01562V10.7031C0.625 10.8062 0.709375 10.8906 0.8125 10.8906H1.75V18.9531C1.75 19.368 2.08516 19.7031 2.5 19.7031H17.5C17.9148 19.7031 18.25 19.368 18.25 18.9531V10.8906H19.1875C19.2906 10.8906 19.375 10.8062 19.375 10.7031V6.01562C19.375 5.60078 19.0398 5.26562 18.625 5.26562ZM10.7969 3.53125C10.7969 2.62656 11.5328 1.89062 12.4375 1.89062C13.3422 1.89062 14.0781 2.62656 14.0781 3.53125C14.0781 4.43594 13.3422 5.17188 12.4375 5.17188H10.7969V3.53125ZM7.5625 1.89062C8.46719 1.89062 9.20312 2.62656 9.20312 3.53125V5.17188H7.5625C6.65781 5.17188 5.92188 4.43594 5.92188 3.53125C5.92188 2.62656 6.65781 1.89062 7.5625 1.89062ZM2.21875 9.29688V6.85938H9.20312V9.29688H2.21875ZM3.34375 10.8906H9.20312V18.1094H3.34375V10.8906ZM16.6562 18.1094H10.7969V10.8906H16.6562V18.1094ZM17.7812 9.29688H10.7969V6.85938H17.7812V9.29688Z" fill="#262626"/>
</svg>
`

  static orderReviewAction =
    ` <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.03532 0.246226C1.4733 -0.161615 0.675993 -0.0514079 0.25448 0.49238C-0.167033 1.03617 -0.0531312 1.80762 0.508886 2.21546L2.68119 3.79184L6.16525 18.3997C6.43163 19.5166 7.4595 20.3078 8.64416 20.3078H19.7165C20.419 20.3078 20.9885 19.7567 20.9885 19.077C20.9885 18.3973 20.419 17.8462 19.7165 17.8462L8.64416 17.8462L8.31404 16.4621L17.6159 15.7121C20.6131 15.4704 23.0257 13.2304 23.3988 10.3428L23.98 5.84378C24.1698 4.3746 22.9858 3.07692 21.4556 3.07692H5.11559C4.95892 2.57744 4.64159 2.13753 4.20763 1.82261L2.03532 0.246226ZM5.70869 5.53846L7.73603 14.0386L17.4046 13.2591C19.203 13.1141 20.6505 11.77 20.8743 10.0375L21.4556 5.53846H5.70869Z" fill="blue"/>
<path d="M10.1762 22.7692C10.1762 23.449 9.60672 24 8.90419 24C8.20167 24 7.63217 23.449 7.63217 22.7692C7.63217 22.0895 8.20167 21.5385 8.90419 21.5385C9.60672 21.5385 10.1762 22.0895 10.1762 22.7692Z" fill="blue"/>
<path d="M17.8084 24C18.5109 24 19.0804 23.449 19.0804 22.7692C19.0804 22.0895 18.5109 21.5385 17.8084 21.5385C17.1059 21.5385 16.5364 22.0895 16.5364 22.7692C16.5364 23.449 17.1059 24 17.8084 24Z" fill="blue"/>
</svg>
`

  static orderReview =
    ` <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.03532 0.246226C1.4733 -0.161615 0.675993 -0.0514079 0.25448 0.49238C-0.167033 1.03617 -0.0531312 1.80762 0.508886 2.21546L2.68119 3.79184L6.16525 18.3997C6.43163 19.5166 7.4595 20.3078 8.64416 20.3078H19.7165C20.419 20.3078 20.9885 19.7567 20.9885 19.077C20.9885 18.3973 20.419 17.8462 19.7165 17.8462L8.64416 17.8462L8.31404 16.4621L17.6159 15.7121C20.6131 15.4704 23.0257 13.2304 23.3988 10.3428L23.98 5.84378C24.1698 4.3746 22.9858 3.07692 21.4556 3.07692H5.11559C4.95892 2.57744 4.64159 2.13753 4.20763 1.82261L2.03532 0.246226ZM5.70869 5.53846L7.73603 14.0386L17.4046 13.2591C19.203 13.1141 20.6505 11.77 20.8743 10.0375L21.4556 5.53846H5.70869Z" fill="#262626"/>
<path d="M10.1762 22.7692C10.1762 23.449 9.60672 24 8.90419 24C8.20167 24 7.63217 23.449 7.63217 22.7692C7.63217 22.0895 8.20167 21.5385 8.90419 21.5385C9.60672 21.5385 10.1762 22.0895 10.1762 22.7692Z" fill="#262626"/>
<path d="M17.8084 24C18.5109 24 19.0804 23.449 19.0804 22.7692C19.0804 22.0895 18.5109 21.5385 17.8084 21.5385C17.1059 21.5385 16.5364 22.0895 16.5364 22.7692C16.5364 23.449 17.1059 24 17.8084 24Z" fill="#262626"/>
</svg>
`


  static editShopInfo =
    ` <svg width="24" height="28" viewBox="0 0 24 28" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12 3.82227C17.0292 3.82227 21.2499 8.42955 21.25 14.3008C21.25 20.1722 17.0293 24.7803 12 24.7803C6.97073 24.7803 2.75 20.1722 2.75 14.3008C2.75014 8.42955 6.97081 3.82227 12 3.82227Z" stroke="#FF9228" stroke-width="1.5"/>
<path d="M12 7.62402C13.7155 7.62402 15.2499 9.21492 15.25 11.3652C15.25 13.5157 13.7156 15.1074 12 15.1074C10.2844 15.1074 8.75 13.5157 8.75 11.3652C8.75015 9.21492 10.2845 7.62402 12 7.62402Z" stroke="#FF9228" stroke-width="1.5"/>
<path d="M18.6154 21.599C17.1754 19.2266 14.7498 17.6689 12.0001 17.6689C9.25041 17.6689 6.82474 19.2266 5.38477 21.599" stroke="#FF9228" stroke-width="1.5"/>
</svg>
`
  static editShopInfo2 =
    ` <svg width="24" height="28" viewBox="0 0 24 28" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M16.6053 9.10602L8.67777 18.0077C8.34994 18.3757 7.95235 18.6541 7.51586 18.8237L7.32686 18.8901L3.66187 20.0212C3.50821 20.0683 3.36601 19.9357 3.36506 19.7696L3.37285 19.6967L4.38084 15.5821C4.52129 15.0085 4.79212 14.4858 5.1667 14.0652L13.0942 5.16351C14.0638 4.07483 15.6357 4.07481 16.6053 5.16351C17.5748 6.25221 17.5748 8.01732 16.6053 9.10602Z" stroke="#FF9228" stroke-width="1.5"/>
<path d="M12.5078 6.30469L15.6898 9.87767" stroke="#FF9228" stroke-width="1.5" stroke-linecap="round"/>
<path d="M3 23.4072H21" stroke="#FF9228" stroke-width="1.5" stroke-linecap="round"/>
</svg>
`
  static manageProduct =
    ` <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.1667 7.83333H2.83333V6.16667H21.1667V7.83333Z" fill="#FFC043"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.53162 2H16.4684L22 6.60969V22H2V6.60969L7.53162 2ZM8.13504 3.66667L3.66667 7.39031V20.3333H20.3333V7.39031L15.865 3.66667H8.13504Z" fill="#FFC043"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.6025 2H13.3975L15.3333 6.83951V13.6667H8.66667V6.83951L10.6025 2ZM11.7309 3.66667L10.3333 7.16049V12H13.6667V7.16049L12.2691 3.66667H11.7309Z" fill="#FFC043"/>
</svg>
`

  static manageReport =
    ` <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.66667 7.83333V22H7V7.83333H8.66667Z" fill="#FFC043"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.33333 3.66667C4.8731 3.66667 4.5 4.03976 4.5 4.5C4.5 4.96024 4.8731 5.33333 5.33333 5.33333C5.79357 5.33333 6.16667 4.96024 6.16667 4.5C6.16667 4.03976 5.79357 3.66667 5.33333 3.66667ZM2.83333 4.5C2.83333 3.11929 3.95262 2 5.33333 2C6.71405 2 7.83333 3.11929 7.83333 4.5C7.83333 5.88071 6.71405 7 5.33333 7C3.95262 7 2.83333 5.88071 2.83333 4.5Z" fill="#FFC043"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.5 9.5C4.27899 9.5 4.06702 9.5878 3.91074 9.74408C3.75446 9.90036 3.66667 10.1123 3.66667 10.3333V22H2V10.3333C2 9.67029 2.26339 9.03441 2.73223 8.56557C3.20107 8.09673 3.83696 7.83333 4.5 7.83333H15.3333V9.5H4.5Z" fill="#FFC043"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.5 3.66667H22V16.1667H10.3333V14.5H20.3333V5.33333H9.5V3.66667Z" fill="#FFC043"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.9007 21.4949L15.4007 15.6616L16.9326 15.0051L19.4326 20.8384L17.9007 21.4949Z" fill="#FFC043"/>
</svg>
`

  static promotion =
    ` <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 3H24V10H23C21.8523 10 21 10.8523 21 12C21 13.1477 21.8523 14 23 14H24V21H0V14H1C2.14772 14 3 13.1477 3 12C3 10.8523 2.14772 10 1 10H0V3ZM2 5V8.11978C3.74908 8.55019 5 10.0945 5 12C5 13.9055 3.74908 15.4498 2 15.8802V19H22V15.8802C20.2509 15.4498 19 13.9055 19 12C19 10.0945 20.2509 8.55019 22 8.11978V5H2Z" fill="#FFC043"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.4142 9L9 16.4142L7.58579 15L15 7.58579L16.4142 9Z" fill="#FFC043"/>
<path d="M9.5 10C10.3284 10 11 9.32843 11 8.5C11 7.67157 10.3284 7 9.5 7C8.67157 7 8 7.67157 8 8.5C8 9.32843 8.67157 10 9.5 10Z" fill="#FFC043"/>
<path d="M14.5 17C15.3284 17 16 16.3284 16 15.5C16 14.6716 15.3284 14 14.5 14C13.6716 14 13 14.6716 13 15.5C13 16.3284 13.6716 17 14.5 17Z" fill="#FFC043"/>
</svg>
`
  static managePayment =
    ` <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20.625 2.625H3.375C2.96016 2.625 2.625 2.96016 2.625 3.375V20.625C2.625 21.0398 2.96016 21.375 3.375 21.375H20.625C21.0398 21.375 21.375 21.0398 21.375 20.625V3.375C21.375 2.96016 21.0398 2.625 20.625 2.625ZM19.6875 13.5H12.375V10.5H19.6875V13.5ZM19.6875 9H11.625C11.2102 9 10.875 9.33516 10.875 9.75V14.25C10.875 14.6648 11.2102 15 11.625 15H19.6875V19.6875H4.3125V4.3125H19.6875V9Z" fill="#FFC043"/>
<path d="M13.5938 12C13.5938 12.2486 13.6925 12.4871 13.8683 12.6629C14.0442 12.8387 14.2826 12.9375 14.5312 12.9375C14.7799 12.9375 15.0183 12.8387 15.1942 12.6629C15.37 12.4871 15.4688 12.2486 15.4688 12C15.4688 11.7514 15.37 11.5129 15.1942 11.3371C15.0183 11.1613 14.7799 11.0625 14.5312 11.0625C14.2826 11.0625 14.0442 11.1613 13.8683 11.3371C13.6925 11.5129 13.5938 11.7514 13.5938 12Z" fill="#FFC043"/>
</svg>
`


}

