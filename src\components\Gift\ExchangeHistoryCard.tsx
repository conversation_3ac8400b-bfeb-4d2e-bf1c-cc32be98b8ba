import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import { Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../assets/skin/colors';
import { TypoSkin } from '../../assets/skin/typography';
import ConfigAPI from '../../Config/ConfigAPI';
import { ExchangeGiftItem } from '../../modules/gift/giftDA';

interface ExchangeHistoryCardProps {
  item: ExchangeGiftItem;
  onPress?: (item: ExchangeGiftItem) => void;
}

const ExchangeHistoryCard: React.FC<ExchangeHistoryCardProps> = ({
  item,
  onPress,
}) => {
  const getStatusInfo = (status: number) => {
    switch (status) {
      case 0:
        return {
          text: 'Đang chờ duyệt',
          color: '#FFA500',
          backgroundColor: '#FFF3E0',
          icon: 'outline/time/clock',
        };
      case 1:
        return {
          text: 'Đã duyệt',
          color: '#4CAF50',
          backgroundColor: '#E8F5E8',
          icon: 'fill/interface/check-circle',
        };
      case 2:
        return {
          text: 'Từ chối',
          color: '#F44336',
          backgroundColor: '#FFEBEE',
          icon: 'fill/interface/times-circle',
        };
      default:
        return {
          text: 'Không xác định',
          color: '#999999',
          backgroundColor: '#F5F5F5',
          icon: 'outline/interface/question-circle',
        };
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const statusInfo = getStatusInfo(item.Status);

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => onPress && onPress(item)}
      activeOpacity={0.8}>
      <View style={styles.content}>
        {/* Image */}
        <View style={styles.imageContainer}>
          <FastImage
            source={{ uri: ConfigAPI.urlImg + item.Gift?.Img }}
            style={styles.image}
            resizeMode={FastImage.resizeMode.cover}
          />
        </View>

        {/* Info */}
        <View style={styles.info}>
          <Text style={styles.title} numberOfLines={2}>
            {item.Gift?.Name}
          </Text>
          
          <Text style={styles.date}>
            {formatDate(item.DateCreated)}
          </Text>

          <View style={styles.footer}>
            <View style={styles.pointsContainer}>
              <Winicon
                src="fill/shopping/diamond"
                size={14}
                color={ColorThemes.light.primary_main_color}
              />
              <Text style={styles.points}>
                -{item.Points.toLocaleString()} điểm
              </Text>
            </View>

            <View style={[styles.statusBadge, { backgroundColor: statusInfo.backgroundColor }]}>
              <Winicon
                src={statusInfo.icon}
                size={12}
                color={statusInfo.color}
              />
              <Text style={[styles.statusText, { color: statusInfo.color }]}>
                {statusInfo.text}
              </Text>
            </View>
          </View>

          {item.Note && (
            <Text style={styles.note} numberOfLines={2}>
              Ghi chú: {item.Note}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  content: {
    flexDirection: 'row',
    padding: 16,
  },
  imageContainer: {
    marginRight: 12,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  info: {
    flex: 1,
  },
  title: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
  },
  date: {
    ...TypoSkin.caption,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  points: {
    ...TypoSkin.body2,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '600',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    ...TypoSkin.caption,
    fontWeight: '500',
  },
  note: {
    ...TypoSkin.caption,
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontStyle: 'italic',
    marginTop: 4,
  },
});

export default ExchangeHistoryCard;
