import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import { ComponentStatus, FLoading, showSnackbar, Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../assets/skin/colors';
import { TypoSkin } from '../../assets/skin/typography';
import { useSelectorCustomerState } from '../../redux/hook/customerHook';
import HeaderShop from '../../components/shop/HeaderShop';
import NavigateShop from '../../components/shop/NavigateShop';
import GiftDA, { GiftItem } from '../../modules/gift/giftDA';
import ConfigAPI from '../../Config/ConfigAPI';

const { width } = Dimensions.get('window');

const GiftDetail = () => {
  const route = useRoute<any>();
  const navigation = useNavigation();
  const customer = useSelectorCustomerState().data;
  const giftDA = new GiftDA();

  const { giftId } = route.params;

  // States
  const [loading, setLoading] = useState(false);
  const [gift, setGift] = useState<GiftItem | null>(null);
  const [currentPoints, setCurrentPoints] = useState(0);

  // Fetch gift detail
  const fetchGiftDetail = async () => {
    if (!giftId) return;

    setLoading(true);
    try {
      const giftData = await giftDA.getGiftDetail(giftId);
      if (giftData) {
        setGift(giftData);
      } else {
        showSnackbar({
          message: 'Không thể tải thông tin quà tặng',
          status: ComponentStatus.ERROR,
        });
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error fetching gift detail:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi tải thông tin quà tặng',
        status: ComponentStatus.ERROR,
      });
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  // Fetch current points
  const fetchCurrentPoints = async () => {
    if (!customer?.Id) return;
    try {
      const points = await giftDA.getCurrentPoints(customer.Id);
      setCurrentPoints(points);
    } catch (error) {
      console.error('Error fetching current points:', error);
    }
  };

  // Handle exchange gift
  const handleExchangeGift = async () => {
    if (!customer?.Id || !gift) return;

    if (currentPoints < gift.Points) {
      showSnackbar({
        message: 'Số điểm không đủ để đổi quà này',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    const isExpired = gift.DateExpired && new Date(gift.DateExpired) < new Date();
    const isOutOfStock = gift.Quantity !== undefined && gift.Quantity <= 0;

    if (isExpired) {
      showSnackbar({
        message: 'Quà tặng này đã hết hạn',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    if (isOutOfStock) {
      showSnackbar({
        message: 'Quà tặng này đã hết hàng',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    Alert.alert(
      'Xác nhận đổi quà',
      `Bạn có chắc chắn muốn đổi "${gift.Name}" với ${gift.Points.toLocaleString()} điểm?`,
      [
        { text: 'Hủy', style: 'cancel' },
        {
          text: 'Đồng ý',
          onPress: async () => {
            try {
              setLoading(true);
              const response = await giftDA.exchangeGift(gift.Id!, customer.Id, gift.Points);
              
              if (response?.code === 200) {
                showSnackbar({
                  message: 'Đổi quà thành công! Vui lòng chờ duyệt.',
                  status: ComponentStatus.SUCCSESS,
                });
                
                // Refresh current points
                await fetchCurrentPoints();
                
                // Navigate back
                navigation.goBack();
              } else {
                showSnackbar({
                  message: 'Không thể đổi quà. Vui lòng thử lại.',
                  status: ComponentStatus.ERROR,
                });
              }
            } catch (error) {
              console.error('Error exchanging gift:', error);
              showSnackbar({
                message: 'Có lỗi xảy ra khi đổi quà',
                status: ComponentStatus.ERROR,
              });
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  useEffect(() => {
    if (customer?.Id) {
      fetchGiftDetail();
      fetchCurrentPoints();
    }
  }, [giftId, customer?.Id]);

  if (!gift) {
    return (
      <View style={styles.container}>
        <FLoading visible={loading} />
        <View style={styles.header}>
          <HeaderShop />
        </View>
        <NavigateShop title="Chi tiết quà tặng" />
      </View>
    );
  }

  const canExchange = currentPoints >= gift.Points;
  const isExpired = gift.DateExpired && new Date(gift.DateExpired) < new Date();
  const isOutOfStock = gift.Quantity !== undefined && gift.Quantity <= 0;

  return (
    <View style={styles.container}>
      <FLoading visible={loading} />
      
      {/* Header */}
      <View style={styles.header}>
        <HeaderShop />
      </View>
      <NavigateShop title="Chi tiết quà tặng" />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Image */}
        <View style={styles.imageContainer}>
          <FastImage
            source={{ uri: ConfigAPI.urlImg + gift.Img }}
            style={styles.image}
            resizeMode={FastImage.resizeMode.cover}
          />
          
          {/* Status badges */}
          {isExpired && (
            <View style={[styles.badge, styles.expiredBadge]}>
              <Text style={styles.badgeText}>Hết hạn</Text>
            </View>
          )}
          {isOutOfStock && (
            <View style={[styles.badge, styles.outOfStockBadge]}>
              <Text style={styles.badgeText}>Hết hàng</Text>
            </View>
          )}
        </View>

        {/* Content */}
        <View style={styles.detailContent}>
          {/* Title */}
          <Text style={styles.title}>{gift.Name}</Text>

          {/* Points */}
          <View style={styles.pointsContainer}>
            <Winicon
              src="fill/shopping/diamond"
              size={20}
              color={ColorThemes.light.primary_main_color}
            />
            <Text style={styles.points}>{gift.Points.toLocaleString()} điểm</Text>
          </View>

          {/* Info */}
          <View style={styles.infoContainer}>
            {gift.DateExpired && (
              <View style={styles.infoRow}>
                <Winicon
                  src="outline/time/calendar"
                  size={16}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
                <Text style={styles.infoText}>
                  Hết hạn: {formatDate(gift.DateExpired)}
                </Text>
              </View>
            )}

            {gift.Quantity !== undefined && (
              <View style={styles.infoRow}>
                <Winicon
                  src="outline/shopping/package"
                  size={16}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
                <Text style={styles.infoText}>
                  Còn lại: {gift.Quantity}
                </Text>
              </View>
            )}

            <View style={styles.infoRow}>
              <Winicon
                src="fill/shopping/diamond"
                size={16}
                color={ColorThemes.light.neutral_text_subtitle_color}
              />
              <Text style={styles.infoText}>
                Điểm của bạn: {currentPoints.toLocaleString()}
              </Text>
            </View>
          </View>

          {/* Description */}
          {gift.Description && (
            <View style={styles.descriptionContainer}>
              <Text style={styles.descriptionTitle}>Mô tả</Text>
              <Text style={styles.description}>{gift.Description}</Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Exchange Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.exchangeButton,
            (!canExchange || isExpired || isOutOfStock) && styles.disabledButton,
          ]}
          onPress={handleExchangeGift}
          disabled={!canExchange || isExpired || isOutOfStock}>
          <Text
            style={[
              styles.exchangeButtonText,
              (!canExchange || isExpired || isOutOfStock) && styles.disabledButtonText,
            ]}>
            {!canExchange
              ? 'Không đủ điểm'
              : isExpired
              ? 'Đã hết hạn'
              : isOutOfStock
              ? 'Hết hàng'
              : 'Đổi ngay'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
    backgroundColor: 'white',
  },
  image: {
    width: width,
    height: width * 0.8,
  },
  badge: {
    position: 'absolute',
    top: 16,
    right: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  expiredBadge: {
    backgroundColor: '#FF4444',
  },
  outOfStockBadge: {
    backgroundColor: '#999999',
  },
  badgeText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  detailContent: {
    backgroundColor: 'white',
    padding: 20,
    marginTop: 8,
  },
  title: {
    ...TypoSkin.heading2,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 16,
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 20,
  },
  points: {
    ...TypoSkin.heading3,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '600',
  },
  infoContainer: {
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  infoText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  descriptionContainer: {
    marginBottom: 20,
  },
  descriptionTitle: {
    ...TypoSkin.title2,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 8,
  },
  description: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_subtitle_color,
    lineHeight: 24,
  },
  footer: {
    backgroundColor: 'white',
    padding: 20,
    paddingBottom: 30,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_border_color,
  },
  exchangeButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: ColorThemes.light.neutral_border_color,
  },
  exchangeButtonText: {
    ...TypoSkin.title3,
    color: 'white',
    fontWeight: '600',
  },
  disabledButtonText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default GiftDetail;
