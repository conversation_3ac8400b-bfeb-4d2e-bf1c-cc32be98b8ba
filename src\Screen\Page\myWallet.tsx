import React, {useState, useRef} from 'react';
import {
  ScrollView,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  ImageBackground,
  Modal,
  Dimensions,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';
import {ComponentStatus, showSnackbar, Winicon} from 'wini-mobile-components';
import LinearGradient from 'react-native-linear-gradient';
import QRCode from 'react-native-qrcode-svg';
import ViewShot from 'react-native-view-shot';
import RNFS from 'react-native-fs';
import HeaderShop from '../../components/shop/HeaderShop';
import NavigateShop from '../../components/shop/NavigateShop';
import {ColorThemes} from '../../assets/skin/colors';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import ConfigAPI from '../../Config/ConfigAPI';
import {navigate, RootScreen} from '../../router/router';

const {width, height} = Dimensions.get('window');

const MyWallet = () => {
  const customer = useSelectorCustomerState().data;
  const [showQRModal, setShowQRModal] = useState(false);
  const viewShotRef = useRef<ViewShot>(null);

  const copyToClipboard = () => {
    Clipboard.setString(customer.RefCode ?? '');
    showSnackbar({
      status: ComponentStatus.SUCCSESS,
      message: 'Đã sao chép link vào clipboard',
    });
  };

  const requestStoragePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        // Kiểm tra Android version
        const androidVersion = Platform.Version;

        // Android 13+ (API 33+) không cần WRITE_EXTERNAL_STORAGE
        if (androidVersion >= 33) {
          return true;
        }

        // Kiểm tra quyền hiện tại
        const hasPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        );

        if (hasPermission) {
          return true;
        }

        // Xin quyền
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          {
            title: 'Quyền lưu trữ',
            message: 'Ứng dụng cần quyền truy cập bộ nhớ để lưu mã QR',
            buttonNeutral: 'Hỏi lại sau',
            buttonNegative: 'Từ chối',
            buttonPositive: 'Đồng ý',
          },
        );

        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn('Permission error:', err);
        return false;
      }
    }
    return true;
  };

  const downloadQRCode = async () => {
    try {
      // Xin quyền trước
      const hasPermission = await requestStoragePermission();
      if (!hasPermission) {
        Alert.alert(
          'Cần quyền truy cập',
          'Vui lòng cấp quyền truy cập bộ nhớ trong Cài đặt để lưu mã QR',
          [
            {text: 'Hủy', style: 'cancel'},
            {
              text: 'Mở Cài đặt',
              onPress: () => {
                // Có thể thêm logic mở Settings app
              },
            },
          ],
        );
        return;
      }

      if (viewShotRef.current && viewShotRef.current.capture) {
        // Hiển thị loading
        showSnackbar({
          status: ComponentStatus.INFOR,
          message: 'Đang tạo và lưu mã QR...',
        });

        const uri = await viewShotRef.current.capture();
        const timestamp = new Date().getTime();
        const fileName = `Chainivo_QR_${timestamp}.png`;

        let downloadPath;
        let successMessage;

        if (Platform.OS === 'ios') {
          downloadPath = `${RNFS.DocumentDirectoryPath}/${fileName}`;
          successMessage = 'QR Code đã được lưu vào Files app';
        } else {
          // Android: Sử dụng Pictures/Chainivo thay vì Downloads
          const androidVersion = Platform.Version;
          debugger;
          if (androidVersion.toString() >= '30') {
            // Android 11+ sử dụng Pictures directory
            const picturesPath = `${RNFS.PicturesDirectoryPath}/Chainivo`;

            // Tạo thư mục nếu chưa có
            const dirExists = await RNFS.exists(picturesPath);
            if (!dirExists) {
              await RNFS.mkdir(picturesPath);
            }

            downloadPath = `${picturesPath}/${fileName}`;
            successMessage =
              'QR Code đã được lưu vào thư mục Pictures/Chainivo';
          } else {
            // Android cũ sử dụng External Storage
            downloadPath = `${RNFS.ExternalStorageDirectoryPath}/Download/${fileName}`;
            successMessage = 'QR Code đã được lưu vào thư mục Downloads';
          }
        }

        await RNFS.copyFile(uri, downloadPath);

        Alert.alert('Thành công!', successMessage, [
          {
            text: 'OK',
            onPress: () => {
              setShowQRModal(false);
              showSnackbar({
                status: ComponentStatus.SUCCSESS,
                message: 'Đã lưu mã QR thành công!',
              });
            },
          },
        ]);
      }
    } catch (error) {
      console.error('Error downloading QR code:', error);
      Alert.alert(
        'Lỗi',
        `Không thể lưu QR Code: ${error || 'Lỗi không xác định'}`,
        [{text: 'OK'}],
      );
    }
  };

  return (
    <ScrollView style={styles.container}>
      <HeaderShop />
      <NavigateShop title={'Ví QR của tôi'} status={1} />

      {/* QR Code Section */}
      <View style={styles.qrSection}>
        <View style={styles.qrContainer}>
          <ImageBackground
            source={require('../../assets/bg-qr.png')}
            style={styles.qrBackground}>
            <View style={styles.qrCodeWrapper}>
              <QRCode
                value={customer.RefCode ?? ''}
                size={110}
                backgroundColor="white"
                color="black"
              />
            </View>
          </ImageBackground>
        </View>
      </View>
      {/* Action Buttons */}
      <View style={styles.actionButtonsContainer}>
        <TouchableOpacity
          style={styles.exportButton}
          onPress={() => setShowQRModal(true)}>
          <Text style={styles.exportButtonText}>Xuất mã QR</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.transferButton}
          onPress={() => navigate(RootScreen.TransferCANPoint)}>
          <Text style={styles.transferButtonText}>Chuyển CAN Point</Text>
        </TouchableOpacity>
      </View>

      {/* QR Export Modal */}
      <Modal
        visible={showQRModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowQRModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.downloadButton}
              onPress={downloadQRCode}>
              <Winicon
                src="outline/arrows/cloud-download"
                size={20}
                color="#333"
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowQRModal(false)}>
              <Winicon src="fill/layout/circle-xmark" size={20} color="#333" />
            </TouchableOpacity>
          </View>

          {/* ViewShot bao bọn toàn bộ nội dung bao gồm cả ImageBackground */}
          <ViewShot
            ref={viewShotRef}
            options={{format: 'png', quality: 0.9}}
            style={styles.viewShotContainer}>
            <ImageBackground
              source={require('../../assets/scan-me.png')}
              style={styles.modalContainer}>
              <View style={styles.qrModalContent}>
                {/* Logo */}
                <View style={styles.logoContainer}>
                  <View style={styles.logoCircle}>
                    <Image
                      source={require('../../assets/splash.png')}
                      style={{width: 270, height: 163}}
                    />
                  </View>
                </View>

                {/* QR Code */}
                <View style={styles.modalQRContainer}>
                  <ImageBackground
                    source={require('../../assets/bg-qr.png')}
                    style={styles.modalQRBackground}>
                    <View style={styles.qrCodeWrapper}>
                      <QRCode
                        value={customer.RefCode ?? ''}
                        size={140}
                        backgroundColor="white"
                        color="black"
                      />
                    </View>
                  </ImageBackground>
                </View>

                {/* User Info */}
                <View style={styles.userInfoContainer}>
                  <Image
                    source={{
                      uri: ConfigAPI.urlImg + customer.AvatarUrl,
                    }}
                    style={styles.userAvatar}
                  />
                  <Text style={styles.userNameText}>
                    {customer?.Name || 'Người dùng'}
                  </Text>
                </View>

                {/* Description */}
                <View style={styles.descriptionContainer}>
                  <Text style={styles.descriptionText}>
                    Hãy tham gia cùng tôi và tận hưởng{'\n'}
                    đặc quyền thành viên của Chainivo
                  </Text>
                </View>
              </View>
            </ImageBackground>
          </ViewShot>
        </View>

        {/* User Info */}
        <View style={styles.userInfoContainer}>
          <Image
            source={{uri: ConfigAPI.urlImg + customer.AvatarUrl}}
            style={styles.userAvatar}
          />
          <Text style={styles.userNameText}>
            {customer?.Name || 'Người dùng'}
          </Text>
        </View>

        {/* Description */}
        <View style={styles.descriptionContainer}>
          <Text style={styles.descriptionText}>
            Hãy tham gia cùng tôi và tận hưởng{'\n'}
            đặc quyền thành viên của Chainivo
          </Text>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    qrSection: {
        alignItems: 'center',
        paddingVertical: 30,
    },
    qrContainer: {
        width: 200,
        height: 200,
        borderRadius: 100,
        overflow: 'hidden',
    },
    qrBackground: {
        width: 200,
        height: 200,
        justifyContent: 'center',
        alignItems: 'center',
    },
    qrCodeWrapper: {
        backgroundColor: 'white',
        padding: 15,
        borderRadius: 36,
    },
    linkSection: {
        paddingHorizontal: 20,
        marginBottom: 20,
    },
    linkTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 10,
    },
    linkContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'white',
        borderRadius: 8,
    },
    linkText: {
        flex: 1,
        fontSize: 14,
        color: ColorThemes.light.primary_main_color,
    },
    copyButton: {
        padding: 5,
    },
    actionButtonsContainer: {
        paddingHorizontal: 20,
        gap: 12,
        marginBottom: 30,
    },
    exportButton: {
        backgroundColor: '#90C8FB',
        paddingVertical: 15,
        borderRadius: 25,
        alignItems: 'center',
    },
    exportButtonText: {
        fontSize: 16,
        fontWeight: '600',
        color: '#343434',
    },
    transferButton: {
        backgroundColor: ColorThemes.light.primary_main_color,
        paddingVertical: 15,
        borderRadius: 25,
        alignItems: 'center',
    },
    transferButtonText: {
        fontSize: 16,
        fontWeight: '600',
        color: '#FFFFFF',
    },
    historySection: {
        paddingHorizontal: 20,
    },
    historyTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 15,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: 'white',
        marginBottom: 15,
        width: '100%',
    },
    headerColumn: {
        flex: 1,
    },
    headerPointsColumn: {
        width: 50,
        alignItems: 'center',
    },
    headerButton: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 5,
    },
    headerText: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
    },
    userList: {
        gap: 0,
    },
    userItem: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'white',
        paddingVertical: 12,
        marginBottom: 10,
    },
    userColumn: {
        flex: 1,
    },
    pointsColumn: {
        width: 50,
        alignItems: 'center',
    },
    userInfo: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    avatar: {
        width: 32,
        height: 32,
        borderRadius: 16,
    },
    userDetails: {
        flex: 1,
    },
    userName: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginBottom: 2,
    },
    userMeta: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    badge: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 2,
    },
    badgeText: {
        fontSize: 12,
        color: '#FF4444',
        fontWeight: '500',
    },
    timeLabel: {
        fontSize: 12,
        color: '#666',
    },
    phoneNumber: {
        fontSize: 12,
        color: '#999',
    },
    points: {
        fontSize: 16,
        fontWeight: '600',
        color: '#4169E1',
    },
    // Modal styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',

    },
    viewShotContainer: {
        width: width * 0.95,
        height: 650,
        borderRadius: 20,
        overflow: 'hidden',
    },
    modalContainer: {
        width: '100%',
        height: '100%',
        backgroundColor: 'transparent',
        borderRadius: 20,
        overflow: 'hidden',
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        position: 'absolute',
        top: 60,
        left: 0,
        right: 10,
        zIndex: 10,
        height: 50,
    },
    downloadButton: {
        padding: 6,
        borderRadius: 20,
        width: 32,
        height: 32,
        backgroundColor: '#F5F5F5',
        position: 'absolute',
        top: 10,
        right: 50,
        zIndex: 2,
        justifyContent: 'center',
        alignItems: 'center',
    },
    closeButton: {
        padding: 6,
        width: 32,
        height: 32,
        borderRadius: 20,
        backgroundColor: '#F5F5F5',
        position: 'absolute',
        top: 10,
        right: 10,
        zIndex: 2,
        justifyContent: 'center',
        alignItems: 'center',
    },
    qrModalContent: {
        alignItems: 'center',
        position: 'relative',
    },
    logoContainer: {
        alignItems: 'center',
        zIndex: 1,
    },
    logoCircle: {
        justifyContent: 'center',
        alignItems: 'center',
        // marginBottom: 20,
        borderRadius: 10,
        overflow: 'hidden',
    },
    modalQRContainer: {
        alignItems: 'center',
        // marginBottom: 30,
        zIndex: 1,
    },
    modalQRBackground: {
        width: 250,
        height: 250,
        borderRadius: 100,
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalQRWrapper: {
        backgroundColor: 'white',
        // padding: 20,
        borderRadius: 15,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    userInfoContainer: {
        alignItems: 'center',
        zIndex: 1,
        flexDirection: 'row',
        gap: 10,
    },
    userAvatar: {
        width: 32,
        height: 32,
        borderRadius: 25,
        marginBottom: 10,
    },
    userNameText: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
    },
    descriptionContainer: {
        alignItems: 'center',
        zIndex: 1,
        marginBottom: 20,
    },
    descriptionText: {
        fontSize: 14,
        color: '#666',
        textAlign: 'center',
        lineHeight: 20,
    },
});

export default MyWallet;
