import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import LinearGradient from 'react-native-linear-gradient';
import { TypoSkin } from "../../../../assets/skin/typography";
import { ColorThemes } from "../../../../assets/skin/colors";
import { AppSvg, Winicon } from "wini-mobile-components";

interface MenuActionProps {
    title: string;
    svgIcon: string;
}
interface MenuNotActionProps {
    title: string;
    svgIcon: string;
    setSelect: any;
    type: string;

}

export const MennuAction = (props: MenuActionProps) => {
    let { title, svgIcon } = props;
    return (
        <LinearGradient
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
            style={styles.activeTabContainer}>
            <View style={styles.tabContent}>
                <AppSvg SvgSrc={svgIcon} size={20} />
                <Text style={[styles.tabText, styles.activeTabText]}>
                    {title}
                </Text>
            </View>
        </LinearGradient>
    );
};

export const MenuNotAction = (props: MenuNotActionProps) => {
    let { title, svgIcon, setSelect, type } = props;
    return (
        <TouchableOpacity
            style={styles.tabContainer}
            onPress={() => setSelect(type)}>
            <View style={styles.tabContent}>
                <AppSvg SvgSrc={svgIcon} size={20} />
                <Text style={[styles.tabText, styles.inactiveTabText]}>
                    {title}
                </Text>
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    tabs: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: 22,
        paddingHorizontal: 16,
        height: 40,
    },
    tabContainer: {
        height: 40,
        borderRadius: 10,
        justifyContent: 'center',
        flex: 1,
        marginHorizontal: 4,
    },
    activeTabContainer: {
        height: 40,
        borderRadius: 10,
        justifyContent: 'center',
        flex: 1,
        marginHorizontal: 4,
    },
    tabContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 8,
    },
    tabText: {
        ...TypoSkin.title3,
        marginLeft: 4,
        height: 22,
    },
    activeTabText: {
        color: ColorThemes.light.primary_main_color,
    },
    inactiveTabText: {
        color: ColorThemes.light.neutral_text_subtitle_color,
    },
});