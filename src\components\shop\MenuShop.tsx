/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  StyleSheet,
  View,
} from 'react-native';
import { TypeMenuShop } from '../../Config/Contanst';
import { MenuShopProps } from '../dto/dto';
import { TypoSkin } from '../../assets/skin/typography';
import { ColorThemes } from '../../assets/skin/colors';
import { MennuAction, MenuNotAction } from '../Field/menu/ManageShopMenu/ManageShopMenu';
import iconSvg from '../../svg/icon';

const MenuShop = (props: MenuShopProps) => {
  const { select, setSelect } = props;

  return (
    <View style={styles.tabs}>
      {select == TypeMenuShop.User ? (
        <MennuAction title="Cá nhân" svgIcon={iconSvg.userIconAction} />
      ) : (
        <MenuNotAction title="Cá nhân" svgIcon={iconSvg.userIcon} setSelect={setSelect} type={TypeMenuShop.User} />
      )}
      {select == TypeMenuShop.Afiliate ? (
        <MennuAction title="Affiliate" svgIcon={iconSvg.afiliateAction} />
      ) : (
        <MenuNotAction title="Affiliate" svgIcon={iconSvg.afiliate} setSelect={setSelect} type={TypeMenuShop.Afiliate} />
      )}
      {select == TypeMenuShop.Shop ? (
        <MennuAction title="Shop" svgIcon={iconSvg.shopAction} />
      ) : (
        <MenuNotAction title="Shop" svgIcon={iconSvg.shop} setSelect={setSelect} type={TypeMenuShop.Shop} />
      )}
      {select == TypeMenuShop.Wallet ? (
        <MennuAction title="Ví" svgIcon={iconSvg.walletAction} />
      ) : (
        <MenuNotAction title="Ví" svgIcon={iconSvg.wallet} setSelect={setSelect} type={TypeMenuShop.Wallet} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 22,
    paddingHorizontal: 16,
    height: 40,
  },
  tabContainer: {
    height: 40,
    borderRadius: 10,
    justifyContent: 'center',
    flex: 1,
    marginHorizontal: 4,
  },
  activeTabContainer: {
    height: 40,
    borderRadius: 10,
    justifyContent: 'center',
    flex: 1,
    marginHorizontal: 4,
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
  },
  tabText: {
    ...TypoSkin.title3,
    marginLeft: 4,
    height: 22,
  },
  activeTabText: {
    color: ColorThemes.light.primary_main_color,
  },
  inactiveTabText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default MenuShop;
