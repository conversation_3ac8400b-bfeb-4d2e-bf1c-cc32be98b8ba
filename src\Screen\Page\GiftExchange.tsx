import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { ComponentStatus, FLoading, showSnackbar, Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../assets/skin/colors';
import { TypoSkin } from '../../assets/skin/typography';
import { useSelectorCustomerState } from '../../redux/hook/customerHook';
import { navigate, RootScreen } from '../../router/router';
import HeaderShop from '../../components/shop/HeaderShop';
import NavigateShop from '../../components/shop/NavigateShop';
import GiftCard from '../../components/Gift/GiftCard';
import ExchangeHistoryCard from '../../components/Gift/ExchangeHistoryCard';
import { GiftCardSkeleton, ExchangeHistoryCardSkeleton } from '../../components/Gift/GiftSkeleton';
import GiftDA, { GiftItem, ExchangeGiftItem } from '../../modules/gift/giftDA';

const PAGE_SIZE = 10;

const GiftExchange = () => {
  const navigation = useNavigation<any>();
  const customer = useSelectorCustomerState().data;
  const giftDA = new GiftDA();

  // States
  const [activeTab, setActiveTab] = useState<'gifts' | 'history'>('gifts');
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  
  // Gifts tab states
  const [gifts, setGifts] = useState<GiftItem[]>([]);
  const [giftCategories, setGiftCategories] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [giftsPage, setGiftsPage] = useState(1);
  const [giftsHasMore, setGiftsHasMore] = useState(true);
  
  // History tab states
  const [exchangeHistory, setExchangeHistory] = useState<ExchangeGiftItem[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<number | undefined>(undefined);
  const [historyPage, setHistoryPage] = useState(1);
  const [historyHasMore, setHistoryHasMore] = useState(true);
  
  // Current points
  const [currentPoints, setCurrentPoints] = useState(0);

  // Fetch current points
  const fetchCurrentPoints = async () => {
    if (!customer?.Id) return;
    try {
      const points = await giftDA.getCurrentPoints(customer.Id);
      setCurrentPoints(points);
    } catch (error) {
      console.error('Error fetching current points:', error);
    }
  };

  // Fetch gift categories
  const fetchGiftCategories = async () => {
    try {
      const categories = await giftDA.getGiftCategories();
      setGiftCategories([
        { id: 'all', name: 'Tất cả', count: 0 },
        ...categories,
      ]);
    } catch (error) {
      console.error('Error fetching gift categories:', error);
    }
  };

  // Fetch gifts
  const fetchGifts = async (page: number = 1, isLoadMore: boolean = false, isRefresh: boolean = false) => {
    if (!customer?.Id) return;

    if (isRefresh) {
      setRefreshing(true);
    } else if (isLoadMore) {
      setLoadingMore(true);
    } else {
      setLoading(true);
    }

    try {
      const response = await giftDA.getGifts(
        page,
        PAGE_SIZE,
        selectedCategory === 'all' ? undefined : selectedCategory
      );

      if (response?.code === 200) {
        const newGifts = response.data || [];
        
        if (isLoadMore) {
          setGifts(prev => [...prev, ...newGifts]);
        } else {
          setGifts(newGifts);
        }
        
        setGiftsHasMore(newGifts.length === PAGE_SIZE);
        setGiftsPage(page);
      } else {
        showSnackbar({
          message: 'Không thể tải danh sách quà tặng',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error fetching gifts:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi tải danh sách quà tặng',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  };

  // Fetch exchange history
  const fetchExchangeHistory = async (page: number = 1, isLoadMore: boolean = false, isRefresh: boolean = false) => {
    if (!customer?.Id) return;

    if (isRefresh) {
      setRefreshing(true);
    } else if (isLoadMore) {
      setLoadingMore(true);
    } else {
      setLoading(true);
    }

    try {
      const response = await giftDA.getExchangeHistory(
        customer.Id,
        page,
        PAGE_SIZE,
        selectedStatus
      );

      if (response?.code === 200) {
        const newHistory = response.data || [];
        
        if (isLoadMore) {
          setExchangeHistory(prev => [...prev, ...newHistory]);
        } else {
          setExchangeHistory(newHistory);
        }
        
        setHistoryHasMore(newHistory.length === PAGE_SIZE);
        setHistoryPage(page);
      } else {
        showSnackbar({
          message: 'Không thể tải lịch sử đổi quà',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error fetching exchange history:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi tải lịch sử đổi quà',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  };

  // Handle exchange gift
  const handleExchangeGift = async (gift: GiftItem) => {
    if (!customer?.Id) return;

    if (currentPoints < gift.Points) {
      showSnackbar({
        message: 'Số điểm không đủ để đổi quà này',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    Alert.alert(
      'Xác nhận đổi quà',
      `Bạn có chắc chắn muốn đổi "${gift.Name}" với ${gift.Points.toLocaleString()} điểm?`,
      [
        { text: 'Hủy', style: 'cancel' },
        {
          text: 'Đồng ý',
          onPress: async () => {
            try {
              setLoading(true);
              const response = await giftDA.exchangeGift(gift.Id!, customer.Id, gift.Points);
              
              if (response?.code === 200) {
                showSnackbar({
                  message: 'Đổi quà thành công! Vui lòng chờ duyệt.',
                  status: ComponentStatus.SUCCSESS,
                });
                
                // Refresh current points and data
                await fetchCurrentPoints();
                if (activeTab === 'gifts') {
                  fetchGifts(1, false, false);
                } else {
                  fetchExchangeHistory(1, false, false);
                }
              } else {
                showSnackbar({
                  message: 'Không thể đổi quà. Vui lòng thử lại.',
                  status: ComponentStatus.ERROR,
                });
              }
            } catch (error) {
              console.error('Error exchanging gift:', error);
              showSnackbar({
                message: 'Có lỗi xảy ra khi đổi quà',
                status: ComponentStatus.ERROR,
              });
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  // Handle gift press
  const handleGiftPress = (gift: GiftItem) => {
    navigate(RootScreen.GiftDetail, { giftId: gift.Id });
  };

  // Handle tab change
  const handleTabChange = (tab: 'gifts' | 'history') => {
    setActiveTab(tab);
    if (tab === 'gifts') {
      if (gifts.length === 0) {
        fetchGifts();
      }
    } else {
      if (exchangeHistory.length === 0) {
        fetchExchangeHistory();
      }
    }
  };

  // Handle category filter
  const handleCategoryFilter = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setGifts([]);
    setGiftsPage(1);
    setGiftsHasMore(true);
  };

  // Handle status filter
  const handleStatusFilter = (status: number | undefined) => {
    setSelectedStatus(status);
    setExchangeHistory([]);
    setHistoryPage(1);
    setHistoryHasMore(true);
  };

  // Load more data
  const handleLoadMore = () => {
    if (activeTab === 'gifts' && giftsHasMore && !loadingMore) {
      fetchGifts(giftsPage + 1, true, false);
    } else if (activeTab === 'history' && historyHasMore && !loadingMore) {
      fetchExchangeHistory(historyPage + 1, true, false);
    }
  };

  // Refresh data
  const handleRefresh = () => {
    fetchCurrentPoints();
    if (activeTab === 'gifts') {
      setGifts([]);
      setGiftsPage(1);
      setGiftsHasMore(true);
      fetchGifts(1, false, true);
    } else {
      setExchangeHistory([]);
      setHistoryPage(1);
      setHistoryHasMore(true);
      fetchExchangeHistory(1, false, true);
    }
  };

  // Initial load and focus effect
  useFocusEffect(
    useCallback(() => {
      if (customer?.Id) {
        fetchCurrentPoints();
        fetchGiftCategories();
        if (activeTab === 'gifts') {
          fetchGifts();
        } else {
          fetchExchangeHistory();
        }
      }
    }, [customer?.Id, selectedCategory, selectedStatus])
  );

  // Filter data when category or status changes
  useEffect(() => {
    if (activeTab === 'gifts') {
      setGifts([]);
      setGiftsPage(1);
      setGiftsHasMore(true);
      fetchGifts();
    }
  }, [selectedCategory]);

  useEffect(() => {
    if (activeTab === 'history') {
      setExchangeHistory([]);
      setHistoryPage(1);
      setHistoryHasMore(true);
      fetchExchangeHistory();
    }
  }, [selectedStatus]);

  return (
    <View style={styles.container}>
      <FLoading visible={loading && !refreshing && !loadingMore} />
      
      {/* Header */}
      <View style={styles.header}>
        <HeaderShop />
      </View>
      <NavigateShop title="Đổi quà" />

      {/* Points Display */}
      <View style={styles.pointsContainer}>
        <View style={styles.pointsContent}>
          <Winicon
            src="fill/shopping/diamond"
            size={20}
            color={ColorThemes.light.primary_main_color}
          />
          <Text style={styles.pointsText}>
            Điểm hiện tại: {currentPoints.toLocaleString()}
          </Text>
        </View>
      </View>

      {/* Tabs */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'gifts' && styles.activeTab]}
          onPress={() => handleTabChange('gifts')}>
          <Text style={[styles.tabText, activeTab === 'gifts' && styles.activeTabText]}>
            Danh sách quà tặng
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'history' && styles.activeTab]}
          onPress={() => handleTabChange('history')}>
          <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>
            Lịch sử đổi quà
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {activeTab === 'gifts' ? (
          <>
            {/* Category Filter */}
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.filterContainer}
              contentContainerStyle={styles.filterContent}>
              {giftCategories.map(category => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.filterButton,
                    selectedCategory === category.id && styles.activeFilterButton,
                  ]}
                  onPress={() => handleCategoryFilter(category.id)}>
                  <Text
                    style={[
                      styles.filterButtonText,
                      selectedCategory === category.id && styles.activeFilterButtonText,
                    ]}>
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Gifts List */}
            <FlatList
              data={gifts}
              keyExtractor={(item, index) => `${item.Id}-${index}`}
              renderItem={({ item }) => (
                <GiftCard
                  item={item}
                  onPress={handleGiftPress}
                  onExchange={handleExchangeGift}
                  currentPoints={currentPoints}
                />
              )}
              ListEmptyComponent={() => (
                loading ? (
                  <View>
                    {[...Array(3)].map((_, index) => (
                      <GiftCardSkeleton key={index} />
                    ))}
                  </View>
                ) : (
                  <View style={styles.emptyContainer}>
                    <Winicon
                      src="outline/shopping/gift"
                      size={48}
                      color={ColorThemes.light.neutral_text_subtitle_color}
                    />
                    <Text style={styles.emptyText}>Không có quà tặng nào</Text>
                  </View>
                )
              )}
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
              }
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.1}
              ListFooterComponent={() =>
                loadingMore ? <GiftCardSkeleton /> : null
              }
              showsVerticalScrollIndicator={false}
            />
          </>
        ) : (
          <>
            {/* Status Filter */}
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.filterContainer}
              contentContainerStyle={styles.filterContent}>
              {[
                { id: undefined, name: 'Tất cả' },
                { id: 0, name: 'Đang chờ duyệt' },
                { id: 1, name: 'Đã duyệt' },
                { id: 2, name: 'Từ chối' },
              ].map(status => (
                <TouchableOpacity
                  key={status.id ?? 'all'}
                  style={[
                    styles.filterButton,
                    selectedStatus === status.id && styles.activeFilterButton,
                  ]}
                  onPress={() => handleStatusFilter(status.id)}>
                  <Text
                    style={[
                      styles.filterButtonText,
                      selectedStatus === status.id && styles.activeFilterButtonText,
                    ]}>
                    {status.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Exchange History List */}
            <FlatList
              data={exchangeHistory}
              keyExtractor={(item, index) => `${item.Id}-${index}`}
              renderItem={({ item }) => (
                <ExchangeHistoryCard item={item} />
              )}
              ListEmptyComponent={() => (
                loading ? (
                  <View>
                    {[...Array(3)].map((_, index) => (
                      <ExchangeHistoryCardSkeleton key={index} />
                    ))}
                  </View>
                ) : (
                  <View style={styles.emptyContainer}>
                    <Winicon
                      src="outline/time/history"
                      size={48}
                      color={ColorThemes.light.neutral_text_subtitle_color}
                    />
                    <Text style={styles.emptyText}>Chưa có lịch sử đổi quà</Text>
                  </View>
                )
              )}
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
              }
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.1}
              ListFooterComponent={() =>
                loadingMore ? <ExchangeHistoryCardSkeleton /> : null
              }
              showsVerticalScrollIndicator={false}
            />
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
  pointsContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  pointsContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  pointsText: {
    ...TypoSkin.title3,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '600',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  tabText: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  activeTabText: {
    color: 'white',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  filterContainer: {
    maxHeight: 50,
    marginBottom: 8,
  },
  filterContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_border_color,
  },
  activeFilterButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    borderColor: ColorThemes.light.primary_main_color,
  },
  filterButtonText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  activeFilterButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginTop: 16,
  },
});

export default GiftExchange;
