import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import { Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../assets/skin/colors';
import { TypoSkin } from '../../assets/skin/typography';
import ConfigAPI from '../../Config/ConfigAPI';
import { GiftItem } from '../../modules/gift/giftDA';
import { Ultis } from '../../utils/Utils';

interface GiftCardProps {
  item: GiftItem;
  onPress: (item: GiftItem) => void;
  onExchange: (item: GiftItem) => void;
  currentPoints: number;
}

const GiftCard: React.FC<GiftCardProps> = ({
  item,
  onPress,
  onExchange,
  currentPoints,
}) => {
  const canExchange = currentPoints >= item.Value;
  const isExpired = item.ExpriseDate && new Date(item.ExpriseDate) < new Date();
  const isOutOfStock = item.Quantity !== undefined && item.Quantity <= 0;

  

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => onPress(item)}
      activeOpacity={0.8}>
      {/* Image */}
      <View style={styles.imageContainer}>
        <FastImage
          source={{ uri: ConfigAPI.urlImg + item.Img }}
          style={styles.image}
          resizeMode={FastImage.resizeMode.cover}
        />
        
        {/* Status badges */}
        {isExpired && (
          <View style={[styles.badge, styles.expiredBadge]}>
            <Text style={styles.badgeText}>Hết hạn</Text>
          </View>
        )}
        {isOutOfStock && (
          <View style={[styles.badge, styles.outOfStockBadge]}>
            <Text style={styles.badgeText}>Hết hàng</Text>
          </View>
        )}
      </View>

      {/* Content */}
      <View style={styles.content}>
        <Text style={styles.title} numberOfLines={2}>
          {item.Name}
        </Text>
        
        <Text style={styles.description} numberOfLines={2}>
          {item.Description}
        </Text>

        {item.ExpriseDate && (
          <Text style={styles.expiredDate}>
            Hết hạn: {Ultis.formatDateTime(item.ExpriseDate, false)}
          </Text>
        )}

        {item.Quantity !== undefined && (
          <Text style={styles.quantity}>
            Còn lại: {item.Quantity}
          </Text>
        )}

        {/* Points and Exchange Button */}
        <View style={styles.footer}>
          <View style={styles.pointsContainer}>
            <Winicon
              src="fill/shopping/diamond"
              size={16}
              color={ColorThemes.light.primary_main_color}
            />
            <Text style={styles.points}>{Ultis.money(item.Value)} điểm</Text>
          </View>

          <TouchableOpacity
            style={[
              styles.exchangeButton,
              (!canExchange || isExpired || isOutOfStock) && styles.disabledButton,
            ]}
            onPress={() => onExchange(item)}
            disabled={!canExchange || isExpired || isOutOfStock}>
            <Text
              style={[
                styles.exchangeButtonText,
                (!canExchange || isExpired || isOutOfStock) && styles.disabledButtonText,
              ]}>
              Đổi ngay
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  imageContainer: {
    position: 'relative',
  },
  image: {
    width: '100%',
    height: 200,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  badge: {
    position: 'absolute',
    top: 8,
    right: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  expiredBadge: {
    backgroundColor: '#FF4444',
  },
  outOfStockBadge: {
    backgroundColor: '#999999',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  content: {
    padding: 16,
  },
  title: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 8,
  },
  description: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 8,
  },
  expiredDate: {
    ...TypoSkin.subtitle3,
    color: '#FF4444',
    marginBottom: 4,
  },
  quantity: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 12,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  points: {
    ...TypoSkin.title4,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '600',
  },
  exchangeButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 18,
  },
  disabledButton: {
    backgroundColor: ColorThemes.light.neutral_border_color,
  },
  exchangeButtonText: {
    ...TypoSkin.buttonText3,
    color: 'white',
  },
  disabledButtonText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default GiftCard;
